@font-face {
    font-weight: normal;
    font-style: normal;
    font-family: 'codropsicons';
    src:url('../fonts/codropsicons/codropsicons.eot');
    src:url('../fonts/codropsicons/codropsicons.eot?#iefix') format('embedded-opentype'),
        url('../fonts/codropsicons/codropsicons.woff') format('woff'),
        url('../fonts/codropsicons/codropsicons.ttf') format('truetype'),
        url('../fonts/codropsicons/codropsicons.svg#codropsicons') format('svg');
}

@font-face {
    font-family: 'linecons';
    src:url('../fonts/linecons/linecons.eot?-kux0c3');
    src:url('../fonts/linecons/linecons.eot?#iefix-kux0c3') format('embedded-opentype'),
        url('../fonts/linecons/linecons.woff?-kux0c3') format('woff'),
        url('../fonts/linecons/linecons.ttf?-kux0c3') format('truetype'),
        url('../fonts/linecons/linecons.svg?-kux0c3#linecons') format('svg');
    font-weight: normal;
    font-style: normal;
}

.icon:before {
    font-family: 'linecons';
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    font-size: 2em;
    display: inline-block;
    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-search:before {
    content: "\e600";
}
.icon-settings:before {
    content: "\e601";
}
.icon-bulb:before {
    content: "\e602";
}
.icon-mail:before {
    content: "\e603";
}
.icon-paperplane:before {
    content: "\e604";
}
.icon-megaphone:before {
    content: "\e605";
}
.icon-calendar:before {
    content: "\e606";
}








/* Header */
.codrops-header {
    padding: 0 15px 0;
    text-align: center;
    border-bottom: 1px solid #e1e6ef;
    margin: 0 -15px 30px;
}

.codrops-header h1 {
    margin: 20px 0;
    font-size: 3.5em;
    line-height: 1;
}

.codrops-header h1 span {
    display: block;
    padding: 0.4em 0 0.6em 0.1em;
    font-size: 46%;
    font-weight: 300;
    opacity: 0.7;
}



/* Main content */



.column:first-child {
    box-shadow: inset -1px 0 0 rgba(0,0,0,0.1);
    text-align: right;
}



.column p.small {
    font-size: 1em;
    padding: 0.75em 0 1em;
    font-weight: 700;
    line-height: 1.2;
}

.progress-button {
    position: relative;

}

.progress-button .content {
    position: relative;
    display: block;
    z-index: 10;
    -webkit-transition: -webkit-transform 0.3s;
    transition: transform 0.3s;
}

.progress-button .progress {
    position: absolute;
    left: 0;
    background: rgba(0,0,0,0.2);
    top: 0;
    width: 0%;
    opacity: 0;
    height: 100%;
    z-index: 0;
    -webkit-transition: width 0s 0.3s, opacity 0.3s;
    transition: width 0s 0.3s, opacity 0.3s;
}

.progress-button.active .progress {
    opacity: 1;
    width: 100%;
    -webkit-transition: width 1.2s;
    transition: width 1.2s;
}



@media screen and (max-width: 46.5em) {
    .column {
        width: 100%;
        min-width: auto;
        min-height: auto;
        padding: 2em; 
        font-size: 90%;
    }

    .column:first-child {
        text-align: center;
        box-shadow: inset 0 -1px 0 rgba(0,0,0,0.1);
    }
}

@media screen and (max-width: 25em) {

    .codrops-header h1 {
        font-size: 2.5em;
    }

    .codrops-top a {
        font-size: 1.6em;
        border: 2px solid black;
        border-color: initial;
        padding: 0.5em;
        border-radius: 3px;
    }

    .codrops-icon span {
        display: none;
    }

}