
Social Buttons for Bootstrap
============================
[![npm version](https://badge.fury.io/js/bootstrap-social.svg)](https://badge.fury.io/js/bootstrap-social)
[![Bower version](https://badge.fury.io/bo/bootstrap-social.svg)](https://badge.fury.io/bo/bootstrap-social)

Social Buttons made in pure CSS based on
[Bootstrap](http://twbs.github.io/bootstrap/) and
[Font Awesome](http://fortawesome.github.io/Font-Awesome/)!

[Check the live demo!](http://lipis.github.io/bootstrap-social)

Installation
------------

Include the `bootstrap-social.css` or `bootstrap-social.less` in your project, or
install it through [Bower](http://bower.io/):

    bower install bootstrap-social

Available classes
-----------------
 - `btn-adn`
 - `btn-bitbucket`
 - `btn-dropbox`
 - `btn-facebook`
 - `btn-flickr`
 - `btn-foursquare`
 - `btn-github`
 - `btn-google`
 - `btn-instagram`
 - `btn-linkedin`
 - `btn-microsoft`
 - `btn-odnoklassniki`
 - `btn-openid`
 - `btn-pinterest`
 - `btn-reddit`
 - `btn-soundcloud`
 - `btn-tumblr`
 - `btn-twitter`
 - `btn-vimeo`
 - `btn-vk`
 - `btn-yahoo`

Examples
--------

```html
<a class="btn btn-block btn-social btn-twitter">
  <span class="fa fa-twitter"></span>
  Sign in with Twitter
</a>

<a class="btn btn-social-icon btn-twitter">
  <span class="fa fa-twitter"></span>
</a>
```

Notes
-----
For now I won't accept any request for new buttons as I'm planning to
split the main CSS to have separate files for all of the requested ones.
