<?php
$page = "Vendors";
$link = "vendors.php";
$table = "vendors";
$back = "dashboard.php";
if (isset($_GET["action"]))
    $back = "vendors.php";
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_SESSION["msg"]) && $_SESSION["msg"] != "") {
    $message = $_SESSION["msg"];
    unset($_SESSION["msg"]);
}
if (isset($_GET["action"])) {
    if ($_GET["action"] == "add") {
        $form = "add";
    } elseif ($_GET["action"] == "edit") {
        $form = "edit";
        $id       = mysqli_real_escape_string($connection, $_GET["id"]);
        $strSQL = mysqli_query($connection, "select * from `$table` where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $results    = mysqli_fetch_array($strSQL);
            $name = $results['name'];
            $ratesFrom = $results['ratesFrom'];
            $email = $results['email'];
            $fileType = $results['fileType'];
            $rowStart = $results['rowStart'];
            $dest = $results['dest'];
            $code = $results['code'];
            $price = $results['price'];
            $date = $results['date'];
            $status = $results['status'];
        } else {
            $message = '<div class="alert alert-danger" role="alert">Invalid ' . $page . '</div>';
            $_SESSION["msg"] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        }
    } elseif ($_GET['action'] == "delete") {
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select id from `$table` where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $strSQL = mysqli_query($connection, "delete from `$table` where id='" . $id . "'");

            $message = '<div class="alert alert-danger" role="alert">' . $page . ' deleted</div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        } else {
            $message = '<div class="alert alert-danger" role="alert">Invalid ' . $page . '</div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        }
    }
}
if (isset($_POST['action'])) {
    if ($_POST['action'] == "add") {
        $name = mysqli_real_escape_string($connection, $_POST['name']);
        $ratesFrom = mysqli_real_escape_string($connection, $_POST['ratesFrom']);
        $email = mysqli_real_escape_string($connection, $_POST['email']);
        $fileType = mysqli_real_escape_string($connection, $_POST['fileType']);
        $rowStart = mysqli_real_escape_string($connection, $_POST['rowStart']);
        $dest = mysqli_real_escape_string($connection, $_POST['dest']);
        $code = mysqli_real_escape_string($connection, $_POST['code']);
        $price = mysqli_real_escape_string($connection, $_POST['price']);
        $date = mysqli_real_escape_string($connection, $_POST['date']);
        $status = mysqli_real_escape_string($connection, $_POST['status']);


        $sql = "INSERT INTO `$table` (`name`, `ratesFrom`, `email`, `fileType`, `rowStart`, `dest`, `code`, `price`, `date`, `status`) VALUES('$name', '$ratesFrom', '$email', '$fileType', '$rowStart', '$dest', '$code', '$price', '$date', '$status')";

        mysqli_query($connection, $sql) or die(mysqli_error($connection));
        $id = mysqli_insert_id($connection);




        $message = '<div class="alert alert-success" role="alert">' . $page . ' Added</div>';
        $id = mysqli_insert_id($connection);
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    } elseif ($_POST['action'] == "edit") {
        $name = mysqli_real_escape_string($connection, $_POST['name']);
        $ratesFrom = mysqli_real_escape_string($connection, $_POST['ratesFrom']);
        $email = mysqli_real_escape_string($connection, $_POST['email']);
        $fileType = mysqli_real_escape_string($connection, $_POST['fileType']);
        $rowStart = mysqli_real_escape_string($connection, $_POST['rowStart']);
        $dest = mysqli_real_escape_string($connection, $_POST['dest']);
        $code = mysqli_real_escape_string($connection, $_POST['code']);
        $price = mysqli_real_escape_string($connection, $_POST['price']);
        $date = mysqli_real_escape_string($connection, $_POST['date']);
        $status = mysqli_real_escape_string($connection, $_POST['status']);


        $id     = mysqli_real_escape_string($connection, $_POST['id']);


        $sql = "UPDATE `$table` SET 
`name` = '$name', 
`ratesFrom` = '$ratesFrom', 
`email` = '$email', 
`fileType` = '$fileType', 
`rowStart` = '$rowStart', 
`dest` = '$dest', 
`code` = '$code', 
`price` = '$price', 
`date` = '$date', 
`status` = '$status' $picture WHERE id='$id'";

        $result = mysqli_query($connection, $sql);

        $message = '<div class="alert alert-success" role="alert">' . $page . ' updated</div>';
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    } else {
        $message = '<div class="alert alert-danger" role="alert">Error occured please try again</div>';
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    }
}

?>
<style>
    .form-control {
        width: 100%;
    }

    .input-group-text {
        padding: 4px 14px 4px 0px;
        width: 50%;
        height: 34px;
    }

    .input-group-prepend {
        margin-right: -10px;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }

    .bootstrap-select.form-control-lg .dropdown-toggle {
        padding: 0.375rem 1rem;
    }
</style>
<!-- page content -->
<div class="content-wrapper">
    <section class="content-header">
        <div class="header-icon">
            <i class="fa fa-edit"></i>
        </div>
        <div class="header-title">
            <h1><?php echo $page ?> Management</h1>
            <small>Add, Update or Delete <?php echo $page ?></small>
        </div>
    </section>
    <section class="content">




        <div class="row">
            <div class="col-md-12 grid-margin">
                <div class="d-flex justify-content-between flex-wrap">
                    <div class="d-flex justify-content-between align-items-end flex-wrap">
                        <a href="?action=add" class="btn bb-bg btn-primary bb-bg mt-2 mt-xl-0"><i class="fa fa-plus"></i> Add <?php echo $page ?></a>
                    </div>
                </div>
            </div>
        </div>
        <br>
        <?php echo isset($message) ? $message : ''; ?> <div class="row">
            <div class="col-lg-12 grid-margin stretch-card">
                <div class="card card2">
                    <div class="card-body">
                        <?php if (isset($form)) { ?> <div class="cont1ainer">
                                <form id="dataForm" action="<?php echo $link; ?>" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                                    <?php if ($form == "edit") { ?>
                                        <input type="hidden" name="id" value="<?php echo $id; ?>">
                                    <?php } ?>
                                    <div class="row">
                                        <div class="form-group col-md-6">
                                            <label for="name">Name:</label>
                                            <input type="text" class="form-control" id="name" name="name" value="<?php echo isset($name) ? $name : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="ratesFrom">Rates From Email:</label>
                                            <input type="email" class="form-control" id="ratesFrom" name="ratesFrom" value="<?php echo isset($ratesFrom) ? $ratesFrom : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="email">Account Manager Email:</label>
                                            <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($email) ? $email : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="fileType">Rates File Type:</label>
                                            <select class="form-control" name="fileType" id="_fileType" required>
                                                <option <?php echo (isset($fileType) && $fileType == "1") ? 'selected="selected"' : ''; ?> value="1">XLSX</option>
                                                <option <?php echo (isset($fileType) && $fileType == "2") ? 'selected="selected"' : ''; ?> value="2">CSV</option>
                                            </select>
                                            <div class="invalid-feedback">Please Select a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="dest">Row Start:</label>
                                            <input type="number" class="form-control" id="rowStart" name="rowStart" value="<?php echo isset($rowStart) ? $rowStart : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="dest">Destination Column:</label>
                                            <input type="text" class="form-control" id="dest" name="dest" value="<?php echo isset($dest) ? $dest : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="code">Dial Code Column:</label>
                                            <input type="text" class="form-control" id="code" name="code" value="<?php echo isset($code) ? $code : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="price">Price/Min Column:</label>
                                            <input type="text" class="form-control" id="price" name="price" value="<?php echo isset($price) ? $price : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="date">Effective Date Column:</label>
                                            <input type="text" class="form-control" id="date" name="date" value="<?php echo isset($date) ? $date : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="status">Status:</label>
                                            <select class="form-control" name="status" id="_status" required>
                                                <option <?php echo (isset($status) && $status == "1") ? 'selected="selected"' : ''; ?> value="1">Active</option>
                                                <option <?php echo (isset($status) && $status == "0") ? 'selected="selected"' : ''; ?> value="0">Inactive</option>
                                            </select>
                                            <div class="invalid-feedback">Please Select a Value</div>
                                        </div>
                                        <div class="mt-3 col-lg-12">
                                            <input type="hidden" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">
                                            <button type="submit" id="btnSubmit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        <?php } else { ?> <?php $query = "SELECT * FROM `$table`"; ?> <div class="table-responsive">
                                <table class="table table2" id="myTable">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Account Manager Email</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $result = mysqli_query($connection, $query);
                                        while ($rows = mysqli_fetch_array($result)) {
                                            $status = ($rows['status'] == 1) ? "Active" : "Inactive";
                                            $action = " <a href='?action=edit&id=" . $rows['id'] . "' class='btn-info btn btn-sm'><i class='fa fa-pencil'></i></a> 
                                                        <a href='?action=delete&id=" . $rows['id'] . "' onclick=\"return confirm('Are you sure you want to DELETE this Record?');\" class='btn-danger btn btn-sm'><i class='fa fa-trash-o'></i></a>";

                                            echo "<tr>
                                                    <td>" . $rows['name'] . "</td>
                                                    <td>" . $rows['email'] . "</td>
                                                    <td>" . $status . "</td>
                                                    <td>$action</td>
                                                </tr>";
                                        }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>

    </section>
</div>
<!-- /page content -->
<?php include("includes/footer.php"); ?><script>
    $(function() {
        $("#btnSubmit").on("click", function(e) {
            var form = $("#dataForm")[0];
            var isValid = form.checkValidity();

            if (!isValid) {
                e.preventDefault();
                e.stopPropagation();
            } else {
                form.submit();
            }
            form.classList.add('was-validated');
            return false; // For testing only to stay on this page
        });
    });
</script>