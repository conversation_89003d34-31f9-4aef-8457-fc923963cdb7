<?php
// show errors


ini_set('memory_limit', '1024M'); // or more
set_time_limit(300000000); // seconds
require '/home/<USER>/ratemanager.paragondigicom.com/vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use \PhpOffice\PhpSpreadsheet\Reader\Exception;
// exception 

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

$host       = 'localhost';
$user       = 'paragond_rateManager';
$pass       = 'h6l=%n0kpE)j';
$database   = 'paragond_rateManager';

define("siteurl","https://ratemanager.paragondigicom.com");
define("host", "live"); // local = disabled some features on local, live = fully functional website
$connection = mysqli_connect($host,$user,$pass,$database);

$hostname = '{mail.paragondigicom.com:993/imap/ssl/novalidate-cert}INBOX'; // Added 'novalidate-cert' to ignore certificate errors
$username = '<EMAIL>'; // Replace with your email
$password = '}$RyTQQg[Ytb'; // Replace with your email password

// Try to connect to the mailbox
$inbox = imap_open($hostname, $username, $password) or die('Cannot connect to mail server: ' . imap_last_error());


$totalUpdated = 0;
$totalInserted = 0;


if(isset($_GET['date']) && !empty($_GET['date'])) {
    $today = date("d-M-Y", strtotime($_GET['date']));
} else {
    $today = date("d-M-Y");
}
// $today = "25-07-2025";   
// foreach (range(16, 24) as $day) {
//     $today = "01-06-2025";
//     $today = date("d-M-Y", strtotime($today . " +" . $day . " days"));
//     echo "Searching for emails from: $today <br>";
    


    $formattedDate = strtoupper($today);
    $searchCriteria = 'ON "' . $formattedDate . '"';
    logMessage("Search criteria: " . $searchCriteria);
    
    $emails = imap_search($inbox, $searchCriteria);
    
    // logMessage("Found " . count($emails) . " emails to process");
    
    // If emails are found
    if ($emails) {
        // Sort the emails in reverse order (newest first)
        rsort($emails);

        // Loop through each email
        foreach ($emails as $email_number) {
            // Get the email headers and body
            $overview = imap_fetch_overview($inbox, $email_number, 0);
            $message = imap_fetchbody($inbox, $email_number, 1);

            preg_match('/<(.+)>/', $overview[0]->from, $matches);
            $sender_email = isset($matches[1]) ? $matches[1] : $overview[0]->from;

            // Display the email details

            $sender_email;
            $sql = "SELECT * FROM `vendors` WHERE `ratesFrom` = '$sender_email'";
            echo $sql;
            
            $result = mysqli_query($connection, $sql);
            if (mysqli_num_rows($result) > 0) {
                $rows = mysqli_fetch_assoc($result);
                $vendor_id = $rows['id'];
                // if($vendor_id == 31)
                // {
                //     // skip this vendor
                //     echo "Skipping vendor with ID 31 <br>";
                //     continue;
                // }
                echo "Processing email from: $sender_email <br>";
                
                $file = getAttachment($inbox, $email_number);
                // var_dump($file);
                
                // print_r($rows);
                if ($file == false)
                    continue;
                if ($rows['fileType'] == 1) {
                    // EXLSX FILE
                    try{
                        $inputFileName = $file;
                        $objPHPExcel = IOFactory::load($inputFileName);
                        $sheet = $objPHPExcel->getSheet(0);
                        $highestRow = $sheet->getHighestRow();
                        $highestColumn = $sheet->getHighestColumn();
                        logMessage("Successfully loaded file: $file", "INFO");
                    } catch (Exception $e) {
                        logMessage("Reader error in file: $file - " . $e->getMessage(), "ERROR");
                    } catch (\Exception $e) {
                        logMessage("General error in file: $file - " . $e->getMessage(), "ERROR");
                    }
                    // print_r($rows);
                    // exit;
                    // dest, code, price and date
                    $rowStart = $rows['rowStart'];
                    $destCell = $rows['dest'];
                    $codeCell = $rows['code'];
                    $priceCell = $rows['price'];
                    $dateCell = $rows['date'];

                    $lastDest = "";
                    $lastPrice = "";
                    $lastCode = "";
                    
                    for ($row = 0; $row <= $highestRow; ++$row) {
                        // get data of A6 cell only
                        $dest = $sheet->getCell($destCell.$rowStart)->getValue();
                        $code = $sheet->getCell($codeCell.$rowStart)->getValue();
                        $price = $sheet->getCell($priceCell.$rowStart)->getValue();
                        if($lastDest == $dest && $lastPrice == $price && $lastCode == $code)
                        {
                            // skip this row
                            // echo "Skipping row $rowStart <br>";
                            $rowStart++;
                            continue;
                        }
                        $lastCode = $code;
                        $lastDest = $dest;
                        $lastPrice = $price;
                        // $date contain A5-ALL tag then remove -ALL and get date from A5
                        
                        if (strpos($dateCell, '-All') !== false) {
                            $dateCellOne = str_replace("-All", "", $dateCell);
                            
                            $date = $sheet->getCell($dateCellOne)->getValue();
                            $date = strTODateRegx($date);
                        }
                        else
                        {
                            
                            $date = $sheet->getCell($dateCell.$rowStart)->getValue();
                            if (Date::isDateTime($sheet->getCell($dateCell.$rowStart))) {
                                $timestamp = Date::excelToTimestamp($date);
                                $date = date('Y-m-d', $timestamp); // Format it to Y-m-d
                            }
                            else if($date == "Immediate")
                            {
                                $date = date('Y-m-d', time());
                            }
                            else
                            {
                                $date = date("Y-m-d", strtotime($date));
                            }
                        }
                        
                        if($dest == "" || $code == "" || $price == "" || $date == "")
                        {
                            $rowStart++;
                            continue;
                        }
                        
                        
                        
                        if (strpos($dest, 'U.K.') !== false) {
                            $dest = str_replace("U.K.", "UNITED KINGDOM ", $dest);
                        }
                        if (preg_match('/^UK/', $dest)) {
                            $dest = preg_replace('/^UK/', 'UNITED KINGDOM ', $dest);
                        }
                        if (preg_match('/^USA/', $dest)) {
                            $dest = preg_replace('/^USA/', 'UNITED STATES ', $dest);
                        }
                        $dest = mysqli_real_escape_string($connection,$dest);
                        


                        // select destination if exist update rate and date
                        $sql = "SELECT * FROM `lcr` WHERE `vendor` = '$vendor_id' AND `destination` = '$dest' and `code` = '$code'";
                        // echo $sql."<br>";
                        $result1 = mysqli_query($connection, $sql);
                        if (mysqli_num_rows($result1) > 0) {
                            $sql = "UPDATE `lcr` SET `price` = '$price', `date` = '$date' WHERE `vendor` = '$vendor_id' AND `destination` = '$dest' and `code` = '$code'";
                            // echo $sql."<br>";
                            mysqli_query($connection, $sql);
                            $totalUpdated++;
                        } else {
                            $sql = "INSERT INTO `lcr`(`vendor`, `destination`, `code`, `price`, `date`) VALUES ('$vendor_id', '$dest', '$code', '$price', '$date')";
                            // echo $sql."<br>";
                            mysqli_query($connection, $sql);
                            $totalInserted++;
                        }
                        $rowStart++;
                    }











                    
                } elseif ($rows['fileType'] == 2) {
                    // CSV FILE
                    $file = fopen($file, 'r');
                    $header = fgetcsv($file);

                    $rowStart = $rows['rowStart'] - 1;
                    $destCell = $rows['dest'];
                    $codeCell = $rows['code'];
                    $priceCell = $rows['price'];
                    $dateCell = $rows['date'];


                    $dataCounter = 0;
                    while ($row = fgetcsv($file)) {
                        $dest = $row[alphaToNumber($destCell)];
                        $code = $row[alphaToNumber($codeCell)];
                        $price = $row[alphaToNumber($priceCell)];
                        $date = $row[alphaToNumber($dateCell)];

                        if ($dataCounter <= $rowStart) {
                            // echo "$dataCounter <= $rowStart <br>";
                            $dataCounter++;
                            continue;
                        }


                        if ($date == "Immediate") {
                            $date = date('Y-m-d', time());
                        } else {
                            $date = date("Y-m-d", strtotime($date));
                        }
                        // select destination if exist update rate and date
                        $sql = "SELECT * FROM `lcr` WHERE `vendor` = '$vendor_id' AND `destination` = '$dest' and `code` = '$code'";
                        // echo $sql."<br>";
                        $result1 = mysqli_query($connection, $sql);
                        if (mysqli_num_rows($result1) > 0) {
                            $sql = "UPDATE `lcr` SET `price` = '$price', `date` = '$date' WHERE `vendor` = '$vendor_id' AND `destination` = '$dest'";
                            // echo $sql."<br>";
                            mysqli_query($connection, $sql);
                            $totalUpdated++;
                        } else {
                            $sql = "INSERT INTO `lcr`(`vendor`, `destination`, `code`, `price`, `date`) VALUES ('$vendor_id', '$dest', '$code', '$price', '$date')";
                            // echo $sql."<br>";
                            mysqli_query($connection, $sql);
                            $totalInserted++;
                        }

                        
                    }
                }
                $objPHPExcel->disconnectWorksheets();
                unset($objPHPExcel);
            }
            
        }
        echo "\nTotal inserted: $totalInserted \n";
        echo "Total updated: $totalUpdated \n";
    } else {
        echo "No emails found.";
    }
// }

// Close the connection
imap_close($inbox);

function alphaToNumber($alpha)
{
    // A=0, B=1, C=2, D=3, E=4, F=5, G=6, H=7, I=8, J=9, K=10, L=11
    $alpha = strtoupper($alpha);
    $alpha = str_split($alpha);
    $number = 0;
    $length = count($alpha);
    for ($i = 0; $i < $length; $i++) {
        $number += (ord($alpha[$i]) - 65) * pow(26, $length - $i - 1);
    }
    return $number;
}
function getAttachment2($inbox, $latest_email_number)
{
    // Fetch the structure of the email
    $structure = imap_fetchstructure($inbox, $latest_email_number);

    // Check if there are any attachments
    if (isset($structure->parts) && count($structure->parts)) {
        for ($i = 0; $i < count($structure->parts); $i++) {
            $attachment = $structure->parts[$i];

            // Check if it's an attachment (MIME type base64 or quoted-printable)
            if ($attachment->ifdparameters) {
                foreach ($attachment->dparameters as $object) {
                    if (strtolower($object->attribute) == 'filename') {
                        // Decode and save the attachment
                        $filename = $object->value;
                        $content = imap_fetchbody($inbox, $latest_email_number, $i + 1);

                        // Decode based on the encoding type
                        if ($attachment->encoding == 3) { // Base64
                            $content = base64_decode($content);
                        } elseif ($attachment->encoding == 4) { // Quoted-Printable
                            $content = quoted_printable_decode($content);
                        }

                        // Save the attachment to a file
                        $file_path = '/home/<USER>/ratemanager.paragondigicom.com/ratesFiles/' . $filename; // Save in the current directory
                        file_put_contents($file_path, $content);

                        // echo "Attachment $filename has been saved to $file_path<br>";
                        return $file_path;
                    }
                }
            }
        }
    } else {
        // echo "No attachments found in this email.";
        return false;
    }
}

function strTODateRegx($string)
{
    $regex = '/(\b\d{1,2}[\/\-\.\s](?:\d{1,2}[\/\-\.\s])?\d{2,4}\b)|(\b\w+\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4})/i';

    // Match all possible dates
    if (preg_match_all($regex, $string, $matches)) {
        // Loop through all matched date strings
        foreach ($matches[0] as $dateString) {
            // Try different date formats
            $formats = ['Y-m-d', 'm/d/Y', 'd/m/Y', 'F j, Y', 'F jS, Y'];

            $validDate = false;

            // Try parsing the matched date with each format
            foreach ($formats as $format) {
                $date = DateTime::createFromFormat($format, $dateString);
                if ($date && $date->format($format) === $dateString) {
                    return $date->format('Y-m-d');
                    $validDate = true;
                    break;
                }
            }

            // If the date was not parsed using the predefined formats, try a fallback
            if (!$validDate) {
                $date = date_create($dateString);
                if ($date) {
                    return $date->format('Y-m-d');
                }
            }
        }
    }
}


// include_once('/home/<USER>/ratemanager.paragondigicom.com/includes/config.php');
// require '/home/<USER>/ratemanager.paragondigicom.com/vendor/autoload.php';

// use PhpOffice\PhpSpreadsheet\Spreadsheet;
// use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
// use PhpOffice\PhpSpreadsheet\IOFactory;
// use PhpOffice\PhpSpreadsheet\Style\Alignment;
// use PhpOffice\PhpSpreadsheet\Shared\Date;

// $host       = 'localhost';
// $user       = 'paragond_rateManager';
// $pass       = 'h6l=%n0kpE)j';
// $database   = 'paragond_rateManager';

// define("siteurl","https://ratemanager.paragondigicom.com");
// define("host", "live"); // local = disabled some features on local, live = fully functional website
// $connection = mysqli_connect($host,$user,$pass,$database);

// $hostname = '{mail.paragondigicom.com:993/imap/ssl/novalidate-cert}INBOX'; // Added 'novalidate-cert' to ignore certificate errors
// $username = '<EMAIL>'; // Replace with your email
// $password = '}$RyTQQg[Ytb'; // Replace with your email password

// // Directory to save attachments
// $attachmentDir = __DIR__ . '/attachments/';
// if (!file_exists($attachmentDir)) {
//     mkdir($attachmentDir, 0777, true);
// }

// // Try to connect to the mailbox
// $inbox = @imap_open($hostname, $username, $password);
// if (!$inbox) {
//     logMessage("Cannot connect to mail server: " . imap_last_error(), "ERROR");
//     die();
// }

// // Search for today's emails
// if(isset($_GET['date']) && !empty($_GET['date'])) {
//     $today = date("d-M-Y", strtotime($_GET['date']));
// } else {
//     $today = date("d-M-Y");
// }
// // $today = date("d-M-Y");
// echo "Searching for emails from: $today <br>";
// // logMessage("Searching for emails from: $today");

// // // Try different search criteria
// // $emails = imap_search($inbox, 'UNSEEN SINCE "'.$today.'"');
// // if (!$emails) {
//     // If no unread emails found, try all emails from today
//     // logMessage("No unread emails found, trying all emails from today");
//     // Search criteria based on whether date was provided

//     // $searchCriteria = 'ON "' . $searchDate . '"';

//     // Convert date to proper format for IMAP search
//     $searchDate = date('d-M-Y', strtotime($_GET['date']));
//     // For debugging
//     logMessage("Searching for date: " . $searchDate);
    
//     // Try multiple date formats for search
//     $formattedDate = strtoupper(date('d-M-Y', strtotime($searchDate)));
//     $searchCriteria = 'ON "' . $formattedDate . '"';
//     logMessage("Search criteria: " . $searchCriteria);
    
//     $emails = imap_search($inbox, $searchCriteria);
// // }

// if (!$emails) {
//     logMessage("No emails found for processing", "WARNING");
//     imap_close($inbox);
//     die("No emails found.");
// }

// logMessage("Found " . count($emails) . " emails to process");

// // Sort emails newest first
// rsort($emails);

// // Logging function
function logMessage($message, $type = 'INFO') {
    // $logFile = __DIR__ . '/email_fetch.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] [$type] $message\n";
    // file_put_contents($logFile, $logMessage, FILE_APPEND);
    echo $logMessage."<br>"; // Also output to console if running from command line
}

// // Add this function at the top of the file, after other function definitions
// function parseExcelDate($dateValue, $dateFormatted) {
//     logMessage("Parsing date - Raw: $dateValue, Formatted: $dateFormatted");
    
//     // If it's "Immediate", return current date
//     if ($dateValue == "Immediate" || $dateFormatted == "Immediate") {
//         return date('Y-m-d');
//     }
    
//     // Try to parse as Excel date (numeric)
//     if (is_numeric($dateValue)) {
//         // Excel's date system has two bases: 1900 and 1904
//         // 1900 system starts from 1899-12-31, 1904 system from 1904-01-01
//         $timestamp = Date::excelToTimestamp($dateValue);
//         if ($timestamp !== false && $timestamp > 0) {
//             $date = date('Y-m-d', $timestamp);
//             logMessage("Parsed as Excel numeric date: $date");
//             return $date;
//         }
//     }
    
//     // Try common date formats
//     $formats = [
//         'd-m-Y', 'd/m/Y', 'Y-m-d', 'Y/m/d',
//         'd-M-Y', 'd/M/Y', 'M-d-Y', 'M/d/Y',
//         'd.m.Y', 'Y.m.d',
//         'd-m-y', 'd/m/y', 'y-m-d', 'y/m/d'
//     ];
    
//     // First try with formatted value
//     foreach ($formats as $format) {
//         $date = DateTime::createFromFormat($format, $dateFormatted);
//         if ($date !== false) {
//             $result = $date->format('Y-m-d');
//             logMessage("Parsed with format $format: $result");
//             return $result;
//         }
//     }
    
//     // If formatted value failed, try with raw value
//     foreach ($formats as $format) {
//         $date = DateTime::createFromFormat($format, $dateValue);
//         if ($date !== false) {
//             $result = $date->format('Y-m-d');
//             logMessage("Parsed raw value with format $format: $result");
//             return $result;
//         }
//     }
    
//     // Try strtotime as last resort
//     $timestamp = strtotime($dateFormatted);
//     if ($timestamp !== false && $timestamp > 0) {
//         $date = date('Y-m-d', $timestamp);
//         logMessage("Parsed using strtotime: $date");
//         return $date;
//     }
    
//     logMessage("Failed to parse date value", "ERROR");
//     return false;
// }

// // Loop through each email
// foreach ($emails as $email_number) {
//     try {
//         logMessage("Processing email #$email_number");

//         // Get the email headers and body
//         $overview = imap_fetch_overview($inbox, $email_number, 0);
//         $message = imap_fetchbody($inbox, $email_number, 1);

//         preg_match('/<(.+)>/', $overview[0]->from, $matches);
//         $sender_email = isset($matches[1]) ? $matches[1] : $overview[0]->from;

//         logMessage("Processing email from: $sender_email");

//         // Check vendor
//         $sql = "SELECT * FROM `vendors` WHERE `ratesFrom` = '$sender_email'";
//         logMessage("Checking vendor with query: $sql");
        
//         $result = mysqli_query($connection, $sql);
//         if (mysqli_num_rows($result) > 0) {
//             $rows = mysqli_fetch_assoc($result);
//             $vendor_id = $rows['id'];
//             logMessage("Found vendor ID: $vendor_id");

//             $file = getAttachment($inbox, $email_number);
//             if ($file === false) {
//                 logMessage("No valid attachment found for this email", "WARNING");
//                 continue;
//             }

//             logMessage("Processing file: $file");
//             $processed = false;
            
//             try {
//                 if ($rows['fileType'] == 1) {
//                     // Excel File (XLS or XLSX)
//                     logMessage("Processing as Excel file");
//                     $processed = processExcelFile($file, $rows, $connection);
//                 } elseif ($rows['fileType'] == 2) {
//                     // CSV File
//                     logMessage("Processing as CSV file");
//                     $processed = processCsvFile($file, $rows, $connection);
//                 } else {
//                     logMessage("Unknown file type: " . $rows['fileType'], "ERROR");
//                 }

//                 // Clean up the file after processing
//                 if (file_exists($file)) {
//                     unlink($file);
//                     logMessage("Cleaned up temporary file: $file");
//                 }

//                 if ($processed) {
//                     logMessage("Successfully processed email #$email_number");
//                     // Mark email as processed if needed
//                     imap_setflag_full($inbox, $email_number, "\\Seen");
//                 } else {
//                     logMessage("Failed to process email #$email_number", "ERROR");
//                 }
//             } catch (Exception $e) {
//                 logMessage("Error processing file: " . $e->getMessage(), "ERROR");
//                 // Clean up file if it exists, even if processing failed
//                 if (file_exists($file)) {
//                     unlink($file);
//                     logMessage("Cleaned up temporary file after error: $file");
//                 }
//             }
//         } else {
//             logMessage("No vendor found for email: $sender_email", "WARNING");
//         }
//     } catch (Exception $e) {
//         logMessage("Error processing email #$email_number: " . $e->getMessage(), "ERROR");
//     }
    
//     // Add a small delay between processing emails to prevent overload
//     usleep(100000); // 0.1 second delay
// }

// logMessage("Finished processing all emails");
// imap_close($inbox);

// function alphaToNumber($alpha)
// {
//     // A=0, B=1, C=2, D=3, E=4, F=5, G=6, H=7, I=8, J=9, K=10, L=11
//     $alpha = strtoupper($alpha);
//     $alpha = str_split($alpha);
//     $number = 0;
//     $length = count($alpha);
//     for ($i = 0; $i < $length; $i++) {
//         $number += (ord($alpha[$i]) - 65) * pow(26, $length - $i - 1);
//     }
//     return $number;
// }
function getAttachment($inbox, $email_number) {
    logMessage("Starting attachment extraction for email #$email_number");
    
    // Get email overview for debugging
    $overview = imap_fetch_overview($inbox, $email_number, 0);
    $subject = $overview[0]->subject ?? 'No Subject';
    logMessage("Processing email with subject: $subject");
    
    // Fetch the structure of the email
    $structure = imap_fetchstructure($inbox, $email_number);
    
    if (!$structure) {
        logMessage("Failed to fetch email structure", "ERROR");
        return false;
    }

    logMessage("Email type: " . ($structure->type ?? 'unknown') . ", Subtype: " . ($structure->subtype ?? 'unknown'));

    // Initialize arrays to store attachments
    $attachments = array();

    // Function to check if file is Excel or CSV
    $isValidFileType = function($filename) {
        $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return in_array($ext, ['xls', 'xlsx', 'csv']);
    };

    // Function to recursively search for attachments
    $getAttachmentParts = function($structure, $prefix = "") use (&$getAttachmentParts, $isValidFileType) {
        $attachments = array();

        if (isset($structure->parts)) {
            foreach ($structure->parts as $index => $part) {
                $newPrefix = $prefix . ($prefix ? "." : "") . ($index + 1);
                
                // Check for attachments in various ways
                $filename = null;

                // Check disposition parameters
                if ($part->ifdparameters) {
                    foreach ($part->dparameters as $param) {
                        if (strtolower($param->attribute) == 'filename') {
                            $filename = $param->value;
                            break;
                        }
                    }
                }

                // Check regular parameters if filename not found
                if (!$filename && $part->ifparameters) {
                    foreach ($part->parameters as $param) {
                        if (strtolower($param->attribute) == 'name') {
                            $filename = $param->value;
                            break;
                        }
                    }
                }

                // Check MIME type for Excel files
                $isExcelMime = false;
                if (isset($part->type) && isset($part->subtype)) {
                    $type = strtoupper($part->type);
                    $subtype = strtoupper($part->subtype);
                    
                    // Check for Excel MIME types
                    $excelMimeTypes = [
                        'APPLICATION/VND.MS-EXCEL',
                        'APPLICATION/EXCEL',
                        'APPLICATION/VND.OPENXMLFORMATS-OFFICEDOCUMENT.SPREADSHEETML.SHEET',
                        'APPLICATION/VND.MS-OFFICE',
                        'TEXT/CSV',
                        'TEXT/COMMA-SEPARATED-VALUES'
                    ];
                    
                    $fullMimeType = "APPLICATION/" . $subtype;
                    if (in_array($fullMimeType, $excelMimeTypes)) {
                        $isExcelMime = true;
                        // If no filename was found, create one based on MIME type
                        if (!$filename) {
                            $filename = "spreadsheet_" . time() . ($subtype == 'VND.MS-EXCEL' ? '.xls' : '.xlsx');
                        }
                    }
                }

                // Process only if we have a valid filename
                if ($filename) {
                    // Clean the filename
                    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
                    
                    // Check if it's an Excel or CSV file
                    if ($isValidFileType($filename) || $isExcelMime) {
                        $attachments[] = array(
                            'filename' => $filename,
                            'part_number' => $newPrefix,
                            'encoding' => $part->encoding ?? 0
                        );
                        logMessage("Found valid attachment: $filename");
                    } else {
                        logMessage("Skipping non-Excel/CSV file: $filename");
                    }
                }

                // Recursively check subparts
                if (isset($part->parts)) {
                    $attachments = array_merge($attachments, $getAttachmentParts($part, $newPrefix));
                }
            }
        } elseif ($structure->ifdisposition && 
                  strtolower($structure->disposition) == 'attachment') {
            // Handle single-part emails with attachment
            $filename = null;
            if ($structure->ifdparameters) {
                foreach ($structure->dparameters as $param) {
                    if (strtolower($param->attribute) == 'filename') {
                        $filename = $param->value;
                    }
                }
            }
            if (!$filename && $structure->ifparameters) {
                foreach ($structure->parameters as $param) {
                    if (strtolower($param->attribute) == 'name') {
                        $filename = $param->value;
                    }
                }
            }
            if ($filename && $isValidFileType($filename)) {
                $attachments[] = array(
                    'filename' => $filename,
                    'part_number' => $prefix ?: "1",
                    'encoding' => $structure->encoding ?? 0
                );
                logMessage("Found valid single-part attachment: $filename");
            }
        }
        
        return $attachments;
    };

    // Get all attachments
    $attachments = $getAttachmentParts($structure);
    logMessage("Found " . count($attachments) . " valid Excel/CSV attachments");

    // Process each attachment
    foreach ($attachments as $attachment) {
        logMessage("Processing attachment: " . $attachment['filename']);
        
        $content = imap_fetchbody($inbox, $email_number, $attachment['part_number']);
        
        if (!$content) {
            logMessage("Failed to fetch content for " . $attachment['filename'], "ERROR");
            continue;
        }

        // Decode content based on encoding
        switch ($attachment['encoding']) {
            case 0: // 7BIT
            case 1: // 8BIT
            case 2: // BINARY
                break;
            case 3: // BASE64
                $content = base64_decode($content);
                break;
            case 4: // QUOTED-PRINTABLE
                $content = quoted_printable_decode($content);
                break;
        }

        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $attachment['filename']);
        $file_path = '/home/<USER>/ratemanager.paragondigicom.com/ratesFiles/' . time() . '_' . $filename;
        
        if (file_put_contents($file_path, $content)) {
            logMessage("Successfully saved attachment to: $file_path");
            return $file_path;
        } else {
            logMessage("Failed to save attachment to: $file_path", "ERROR");
        }
    }

    if (empty($attachments)) {
        logMessage("No valid Excel/CSV attachments found in this email", "WARNING");
    }
    
    return false;
}

// function strTODateRegx($string)
// {
//     $regex = '/(\b\d{1,2}[\/\-\.\s](?:\d{1,2}[\/\-\.\s])?\d{2,4}\b)|(\b\w+\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4})/i';

//     // Match all possible dates
//     if (preg_match_all($regex, $string, $matches)) {
//         // Loop through all matched date strings
//         foreach ($matches[0] as $dateString) {
//             // Try different date formats
//             $formats = ['Y-m-d', 'm/d/Y', 'd/m/Y', 'F j, Y', 'F jS, Y'];

//             $validDate = false;

//             // Try parsing the matched date with each format
//             foreach ($formats as $format) {
//                 $date = DateTime::createFromFormat($format, $dateString);
//                 if ($date && $date->format($format) === $dateString) {
//                     return $date->format('Y-m-d');
//                     $validDate = true;
//                     break;
//                 }
//             }

//             // If the date was not parsed using the predefined formats, try a fallback
//             if (!$validDate) {
//                 $date = date_create($dateString);
//                 if ($date) {
//                     return $date->format('Y-m-d');
//                 }
//             }
//         }
//     }
// }