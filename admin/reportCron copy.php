<?php
// include_once('/home/<USER>/ratemanager.paragondigicom.com/includes/config.php');
require '/home/<USER>/ratemanager.paragondigicom.com/vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Shared\Date;

$host       = 'localhost';
$user       = 'paragond_rateManager';
$pass       = 'h6l=%n0kpE)j';
$database   = 'paragond_rateManager';

define("siteurl","https://ratemanager.paragondigicom.com");
define("host", "live"); // local = disabled some features on local, live = fully functional website
$connection = mysqli_connect($host,$user,$pass,$database);

$hostname = '{mail.paragondigicom.com:993/imap/ssl/novalidate-cert}INBOX'; // Added 'novalidate-cert' to ignore certificate errors
$username = '<EMAIL>'; // Replace with your email
$password = '}$RyTQQg[Ytb'; // Replace with your email password

// Try to connect to the mailbox
$inbox = imap_open($hostname, $username, $password) or die('Cannot connect to mail server: ' . imap_last_error());

// $today = "24-09-2024";
$today = date("d-M-Y");

// Search emails in the inbox
echo 'UNSEEN SINCE "'.$today.'"<br>';
// $emails = imap_search($inbox, 'UNSEEN From "<EMAIL>"');
$emails = imap_search($inbox, 'UNSEEN SINCE "'.$today.'"');


// If emails are found
if ($emails) {
    // Sort the emails in reverse order (newest first)
    rsort($emails);

    // Loop through each email
    foreach ($emails as $email_number) {
        // Get the email headers and body
        $overview = imap_fetch_overview($inbox, $email_number, 0);
        $message = imap_fetchbody($inbox, $email_number, 1);

        preg_match('/<(.+)>/', $overview[0]->from, $matches);
        $sender_email = isset($matches[1]) ? $matches[1] : $overview[0]->from;

        // Display the email details

        $sender_email;
        $sql = "SELECT * FROM `vendors` WHERE `ratesFrom` = '$sender_email'";
        // echo $sql;
        $result = mysqli_query($connection, $sql);
        if (mysqli_num_rows($result) > 0) {
            $rows = mysqli_fetch_assoc($result);
            $vendor_id = $rows['id'];
            $file = getAttachment($inbox, $email_number);
            // var_dump($file);
            print_r($rows);
            if ($file == false)
                continue;
            if ($rows['fileType'] == 1) {
                // EXLSX FILE
                $inputFileName = $file;
                $objPHPExcel = IOFactory::load($inputFileName);
                $sheet = $objPHPExcel->getSheet(0);
                $highestRow = $sheet->getHighestRow();
                $highestColumn = $sheet->getHighestColumn();
                
                // dest, code, price and date
                $rowStart = $rows['rowStart'];
                $destCell = $rows['dest'];
                $codeCell = $rows['code'];
                $priceCell = $rows['price'];
                $dateCell = $rows['date'];

                $lastDest = "";
                $lastPrice = "";
                
                for ($row = 0; $row <= $highestRow; ++$row) {
                    // get data of A6 cell only
                    $dest = $sheet->getCell($destCell.$rowStart)->getValue();
                    $code = $sheet->getCell($codeCell.$rowStart)->getValue();
                    $price = $sheet->getCell($priceCell.$rowStart)->getValue();
                    if($lastDest == $dest && $lastPrice == $price)
                    {
                        $rowStart++;
                        continue;
                    }
                    $lastDest = $dest;
                    $lastPrice = $price;
                    // $date contain A5-ALL tag then remove -ALL and get date from A5
                    
                    if (strpos($dateCell, '-All') !== false) {
                        $dateCellOne = str_replace("-All", "", $dateCell);
                        
                        $date = $sheet->getCell($dateCellOne)->getValue();
                        $date = strTODateRegx($date);
                    }
                    else
                    {
                        
                        $date = $sheet->getCell($dateCell.$rowStart)->getValue();
                        if (Date::isDateTime($sheet->getCell($dateCell.$rowStart))) {
                            $timestamp = Date::excelToTimestamp($date);
                            $date = date('Y-m-d', $timestamp); // Format it to Y-m-d
                        }
                        else if($date == "Immediate")
                        {
                            $date = date('Y-m-d', time());
                        }
                        else
                        {
                            $date = date("Y-m-d", strtotime($date));
                        }
                    }
                    
                    if($dest == "" || $code == "" || $price == "" || $date == "")
                    {
                        $rowStart++;
                        continue;
                    }
                    
                    
                    
                    if (strpos($dest, 'U.K.') !== false) {
                        $dest = str_replace("U.K.", "UNITED KINGDOM ", $dest);
                    }
                    if (preg_match('/^UK/', $dest)) {
                        $dest = preg_replace('/^UK/', 'UNITED KINGDOM ', $dest);
                    }
                    if (preg_match('/^USA/', $dest)) {
                        $dest = preg_replace('/^USA/', 'UNITED STATES ', $dest);
                    }
                    $dest = mysqli_real_escape_string($connection,$dest);
                    


                    // select destination if exist update rate and date
                    $sql = "SELECT * FROM `lcr` WHERE `vendor` = '$vendor_id' AND `destination` = '$dest' and `code` = '$code'";
                    echo $sql."<br>";
                    $result1 = mysqli_query($connection, $sql);
                    if (mysqli_num_rows($result1) > 0) {
                        $sql = "UPDATE `lcr` SET `price` = '$price', `date` = '$date' WHERE `vendor` = '$vendor_id' AND `destination` = '$dest' and `code` = '$code'";
                        echo $sql."<br>";
                        mysqli_query($connection, $sql);
                    } else {
                        $sql = "INSERT INTO `lcr`(`vendor`, `destination`, `code`, `price`, `date`) VALUES ('$vendor_id', '$dest', '$code', '$price', '$date')";
                        echo $sql."<br>";
                        mysqli_query($connection, $sql);
                    }
                    $rowStart++;
                }











                
            } elseif ($row['fileType'] == 2) {
                // CSV FILE
                $file = fopen($file, 'r');
                $header = fgetcsv($file);

                $rowStart = $row['rowStart'] - 1;
                $destCell = $row['dest'];
                $codeCell = $row['code'];
                $priceCell = $row['price'];
                $dateCell = $row['date'];


                $dataCounter = 0;
                while ($row = fgetcsv($file)) {
                    $dest = $row[alphaToNumber($destCell)];
                    $code = $row[alphaToNumber($codeCell)];
                    $price = $row[alphaToNumber($priceCell)];
                    $date = $row[alphaToNumber($dateCell)];

                    if ($dataCounter <= $rowStart) {
                        // echo "$dataCounter <= $rowStart <br>";
                        $dataCounter++;
                        continue;
                    }


                    if ($date == "Immediate") {
                        $date = date('Y-m-d', time());
                    } else {
                        $date = date("Y-m-d", strtotime($date));
                    }
                    // select destination if exist update rate and date
                    $sql = "SELECT * FROM `lcr` WHERE `vendor` = '$vendor_id' AND `destination` = '$dest' and `code` = '$code'";
                    echo $sql."<br>";
                    $result = mysqli_query($connection, $sql);
                    if (mysqli_num_rows($result) > 0) {
                        $sql = "UPDATE `lcr` SET `price` = '$price', `date` = '$date' WHERE `vendor` = '$vendor_id' AND `destination` = '$dest'";
                        echo $sql."<br>";
                        mysqli_query($connection, $sql);
                    } else {
                        $sql = "INSERT INTO `lcr`(`vendor`, `destination`, `code`, `price`, `date`) VALUES ('$vendor_id', '$dest', '$code', '$price', '$date')";
                        echo $sql."<br>";
                        mysqli_query($connection, $sql);
                    }

                    
                }
            }
        }
        continue;
    }
} else {
    echo "No emails found.";
}

// Close the connection
imap_close($inbox);

function alphaToNumber($alpha)
{
    // A=0, B=1, C=2, D=3, E=4, F=5, G=6, H=7, I=8, J=9, K=10, L=11
    $alpha = strtoupper($alpha);
    $alpha = str_split($alpha);
    $number = 0;
    $length = count($alpha);
    for ($i = 0; $i < $length; $i++) {
        $number += (ord($alpha[$i]) - 65) * pow(26, $length - $i - 1);
    }
    return $number;
}
function getAttachment($inbox, $latest_email_number)
{
    // Fetch the structure of the email
    $structure = imap_fetchstructure($inbox, $latest_email_number);

    // Check if there are any attachments
    if (isset($structure->parts) && count($structure->parts)) {
        for ($i = 0; $i < count($structure->parts); $i++) {
            $attachment = $structure->parts[$i];

            // Check if it's an attachment (MIME type base64 or quoted-printable)
            if ($attachment->ifdparameters) {
                foreach ($attachment->dparameters as $object) {
                    if (strtolower($object->attribute) == 'filename') {
                        // Decode and save the attachment
                        $filename = $object->value;
                        $content = imap_fetchbody($inbox, $latest_email_number, $i + 1);

                        // Decode based on the encoding type
                        if ($attachment->encoding == 3) { // Base64
                            $content = base64_decode($content);
                        } elseif ($attachment->encoding == 4) { // Quoted-Printable
                            $content = quoted_printable_decode($content);
                        }

                        // Save the attachment to a file
                        $file_path = '/home/<USER>/ratemanager.paragondigicom.com/ratesFiles/' . $filename; // Save in the current directory
                        file_put_contents($file_path, $content);

                        // echo "Attachment $filename has been saved to $file_path<br>";
                        return $file_path;
                    }
                }
            }
        }
    } else {
        // echo "No attachments found in this email.";
        return false;
    }
}

function strTODateRegx($string)
{
    $regex = '/(\b\d{1,2}[\/\-\.\s](?:\d{1,2}[\/\-\.\s])?\d{2,4}\b)|(\b\w+\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4})/i';

    // Match all possible dates
    if (preg_match_all($regex, $string, $matches)) {
        // Loop through all matched date strings
        foreach ($matches[0] as $dateString) {
            // Try different date formats
            $formats = ['Y-m-d', 'm/d/Y', 'd/m/Y', 'F j, Y', 'F jS, Y'];

            $validDate = false;

            // Try parsing the matched date with each format
            foreach ($formats as $format) {
                $date = DateTime::createFromFormat($format, $dateString);
                if ($date && $date->format($format) === $dateString) {
                    return $date->format('Y-m-d');
                    $validDate = true;
                    break;
                }
            }

            // If the date was not parsed using the predefined formats, try a fallback
            if (!$validDate) {
                $date = date_create($dateString);
                if ($date) {
                    return $date->format('Y-m-d');
                }
            }
        }
    }
}


// // EXLSX FILE
// $inputFileName = $file;
// $objPHPExcel = IOFactory::load($inputFileName);
// $sheet = $objPHPExcel->getSheet(0);
// $highestRow = $sheet->getHighestRow();
// $highestColumn = $sheet->getHighestColumn();

// // dest, code, price and date
// $rowStart = $row['rowStart'];
// $destCell = $row['dest'];
// $codeCell = $row['code'];
// $priceCell = $row['price'];
// $dateCell = $row['date'];

// for ($row = 0; $row <= $highestRow; ++$row) {
//     // get data of A6 cell only
//     $dest = $sheet->getCell($destCell . $rowStart)->getValue();
//     $code = $sheet->getCell($codeCell . $rowStart)->getValue();
//     $price = $sheet->getCell($priceCell . $rowStart)->getValue();
//     $date = $sheet->getCell($dateCell . $rowStart)->getValue();

//     $rowStart++;
//     // $date contain A5-ALL tag then remove -ALL and get date from A5
//     if (strpos($date, '-ALL') !== false) {
//         $date = str_replace("-ALL", "", $date);
//     }

//     if (Date::isDateTime($sheet->getCell($dateCell . $rowStart))) {
//         $timestamp = Date::excelToTimestamp($date);
//         $date = date('Y-m-d', $timestamp); // Format it to Y-m-d
//     } else if ($date == "Immediate") {
//         $date = date('Y-m-d', time());
//     } else {
//         $date = date("Y-m-d", strtotime($date));
//     }


//     // select destination if exist update rate and date
//     $sql = "SELECT * FROM `lcr` WHERE `vendor` = '$vendor_id' AND `destination` = '$dest' and `code` = '$code'";
//     $result = mysqli_query($connection, $sql);
//     if (mysqli_num_rows($result) > 0) {
//         $sql = "UPDATE `lcr` SET `price` = '$price', `date` = '$date' WHERE `vendor` = '$vendor_id' AND `destination` = '$dest'";
//         mysqli_query($connection, $sql);
//     } else {
//         $sql = "INSERT INTO `lcr`(`vendor`, `destination`, `code`, `price`, `date`) VALUES ('$vendor_id', '$dest', '$code', '$price', '$date')";
//         mysqli_query($connection, $sql);
//     }
// }