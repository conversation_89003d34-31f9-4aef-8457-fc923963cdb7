<?php
// download csv file of rates
ini_set('display_errors', '1');
ini_set('display_startup_errors', '1');
error_reporting(E_ALL);
include("../includes/config.php");
include("includes/session.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

// Open Template.xlsx file add data and save as new file

// bold value and set border




if (isset($_SESSION["msg"]) && $_SESSION["msg"] != "") {
    $message = $_SESSION["msg"];
    unset($_SESSION["msg"]);
}
$rateID = false;
if (isset($_GET["id"]) && $_GET["id"] != "") {
    $rateID = mysqli_real_escape_string($connection, $_GET["id"]);
}


$strSQL = mysqli_query($connection, "select * from `rateGenerate` where id='" . $rateID . "'");
if (mysqli_num_rows($strSQL) > 0) {
    $results    = mysqli_fetch_array($strSQL);
    $name = $results['name'];
    $trunk = $results['trunk'];
    $currency = $results['currency'];
    $account = $results['account_id'];
    $prefix = $results['prefix'];
    // select rules from rateRules table
    $strSQL = mysqli_query($connection, "select * from `rateRules` where rate_id='" . $rateID . "'");
    if (mysqli_num_rows($strSQL) > 0) {
        // $filename = $name . ".csv";
        $fileName = $name."_" . date('Y-m-d') . ".xlsx";
        $spreadsheet = \PhpOffice\PhpSpreadsheet\IOFactory::load('../uploads/Template_new.xlsx');
        $sheet = $spreadsheet->getActiveSheet();
        
        $cell = 3;
        
        while ($row = mysqli_fetch_array($strSQL)) {
            $areas = explode(",", $row['areas']);
            foreach($areas as $area)
            {
                $cell++;
                if($area == -1)
                {
                    $destinations = $general->getDestinationNameByID($row['destinations'])." All";
                    $codes = $general->getDestinationCodes($row['destinations']);
                }
                else
                {
                    $destinations = $general->getDestinationNameByID($area);
                    $codes = $general->getDestinationCodes($area);
                }
                
                // print_r($row);
                $rate = $row['rate'];
                $billing_cycle = $row['billing_cycle'];
                $remarks = $row['remarks'];
                $prefix = $general->getPrefixNameByID($row['rate_id']);
                $effectiveDate = $general->dateToRead($row['effective']);




                foreach (range('B', 'H') as $columnID) {
                    if($columnID == 'C')
                        continue;
                    $sheet->getColumnDimension($columnID)->setAutoSize(true);
                }
                


                $sheet->getRowDimension($cell)->setRowHeight(40);
                // text wrap
                
                $sheet->getStyle('C'.$cell)->getAlignment()->setWrapText(true); 
                // cell width auto
                
                // cell height


                // cell height auto

                
                $sheet->getStyle('B'.$cell.':H'.$cell)->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
                $sheet->getStyle('B'.$cell.':H'.$cell)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
                $sheet->getStyle('B'.$cell)->getFont()->setBold(true);
                $sheet->getStyle('B'.$cell.':H'.$cell)->getBorders()->getAllBorders()->setBorderStyle(\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN);
                
                $sheet->getStyle('D'.$cell)->getNumberFormat()->setFormatCode('0.0000');

                
                $sheet->setCellValue('B'.$cell, $destinations);
                $sheet->setCellValue('C'.$cell, $codes);
                $sheet->setCellValue('D'.$cell, $rate);
                $sheet->setCellValue('E'.$cell, $billing_cycle);
                $sheet->setCellValue('F'.$cell, $prefix);
                $sheet->setCellValue('G'.$cell, $remarks);
                $sheet->setCellValue('H'.$cell, $effectiveDate);

            }

        }
        $writer = new Xlsx($spreadsheet);
        $writer->save('../uploads/'.$fileName);
        
        // // Redirect output to a client’s web browser (Xlsx)
        // header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        // header('Content-Disposition: attachment;filename="'.$fileName.'"');
        // header('Cache-Control: max-age=0');
        // // If you're serving to IE 9, then the following may be needed
        // header('Cache-Control: max-age=1');

        // // If you're serving to IE over SSL, then the following may be needed
        // header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
        // header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
        // header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
        // header('Pragma: public'); // HTTP/1.0

        // $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        // $writer->save('php://output');
        header('location: ../uploads/'.$fileName);
        exit;
        exit;
    } else {
        $message = '<div class="alert alert-danger" role="alert">Invalid Records</div>';
        $_SESSION["msg"] = $message;
        echo '<meta http-equiv="refresh" content="0;url=rateGenerate.php" />';
        exit;
    }

} else {
    $message = '<div class="alert alert-danger" role="alert">Invalid Records</div>';
    $_SESSION["msg"] = $message;
    echo '<meta http-equiv="refresh" content="0;url=rateGenerate.php" />';
    exit;
}