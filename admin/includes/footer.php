<footer class="main-footer">
            <strong>Copyright &copy; <?php echo date("Y"); ?>.</strong> All rights reserved.
         </footer>
      </div>
      <script src="../assets/plugins/jQuery/jquery-1.12.4.min.js" ></script>
      <script src="../assets/bootstrap/js/popper.min.js" ></script>
      <script src="../assets/plugins/lobipanel/js/jquery-ui.min.js" ></script>
      <script src="../assets/bootstrap/js/bootstrap.min.js" ></script>
      <script src="../assets/plugins/slimScroll/jquery.nicescroll.min.js" ></script>
      <script src="../assets/dist/js/custom.js" ></script>
      <script src="../assets/dist/js/dashboard.js" ></script>
      <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

      <link href="https://cdn.datatables.net/1.10.21/css/jquery.dataTables.min.css" rel="stylesheet" />
<script src="//cdn.datatables.net/1.10.21/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.print.min.js"></script>

<script>
   


   $(document).ready(function() {
      if($("#myTable").length > 0) {
         $("#myTable").DataTable({
            "columnDefs": [
               { "targets": "sort-date", "type": "date" },
               {targets: 'no-sort',orderable: false}
            ],
            "aaSorting": [],
            pageLength: 50,
            lengthMenu: [
               [100, 200, 500, 1000, -1],
               [100, 200, 500, 1000, 'All']
            ],
            dom: 'Bfrtip',
            buttons: [
               'excel', 'pdf', 'print'
            ]
         });
         $("#myTable td").css("white-space", "initial");
      }
   });
   $(document).ready(function() {
      if($(".mtables").length > 0) {
         $(".mtables").DataTable({
            "columnDefs": [
               { "targets": "sort-date", "type": "date" },
               {targets: 'no-sort',orderable: false}
            ],
            "aaSorting": [],
            pageLength: 50,
            lengthMenu: [
               [100, 200, 500, 1000, -1],
               [100, 200, 500, 1000, 'All']
            ],
            dom: 'Bfrti',
            buttons: [
               'excel', 'pdf', 'print'
            ]
         });
         $(".mtables td").css("white-space", "initial");
      }
   });
</script>


      <?php echo isset($extraJS) ? $extraJS : ''; ?>
   

      <script>
         $(function () {
            $('.selectpicker22').selectpicker({liveSearch:true});
         });
         jQuery(function($) {
      var path = window.location.origin + window.location.pathname;
      $('.sidebar-menu li a').each(function() {
         if (this.href === path) {
            if ($(this).parents('.treeview').length > 0) {
               $(this).parents('.treeview').addClass('active');
               $(this).parents('.treeview-menu').addClass('active').show();
            }
            $(this).parent('li').addClass('active');
         }
      });
   });
      </script>
      
   </body>
</html>
