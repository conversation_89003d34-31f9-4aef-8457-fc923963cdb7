<?php
    include_once('../includes/config.php');
    include_once('includes/session.php');
?>
<!DOCTYPE html>
<html lang="en">
   <head>
      <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <meta name="viewport" content="width=device-width, initial-scale=1">
      <title><?php echo sitename; ?></title>
      <link rel="shortcut icon" href="<?php echo logoMini; ?>" type="image/x-icon">
      <link href="../assets/bootstrap/css/bootstrap.min.css" rel="stylesheet" />
      <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/css/bootstrap-select.min.css">
      <link href="../assets/font-awesome/css/font-awesome.min.css" rel="stylesheet" />
      <link href="../assets/pe-icon-7-stroke/css/pe-icon-7-stroke.css" rel="stylesheet" />
      <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

      <link href="../assets/dist/css/stylecrm.css" rel="stylesheet" />
      
      <style>
         .bootstrap-select.form-control-lg .dropdown-toggle {
            padding: 0.5rem 1rem;
            border: 1px solid #cccccc;
            background-color: white;
         }
      </style>
      <?php echo isset($extraCSS) ? $extraCSS : ''; ?>
   </head>
   <body class="hold-transition sidebar-mini">
      <div id="preloader">
         <div id="status"></div>
      </div>
      <div class="wrapper">
         <header class="main-header">
            <a href="#" class="logo">
               <span class="logo-mini">
               <img src="<?php echo logoMini; ?>" alt="">
               </span>
               <span class="logo-lg">
               <img src="<?php echo logo; ?>" alt="">
               </span>
            </a>
            <nav class="navbar navbar-expand py-0">
               <a href="#" class="sidebar-toggle d-block d-sm-none" data-toggle="offcanvas" role="button" style="color: #ffffff;margin-right: 20px;">
                  <!-- Sidebar toggle button-->
                  <span class="sr-only">Toggle navigation</span>
                  <i class="fa fa-bars"></i>
               </a>
               <?php if(isset($back)){ ?>
               <a href="<?php echo $back; ?>" class="sidebar-toggle" data-toggle="offcan2vas" role="button">
                  <span class="sr-only">Toggle navigation</span>
                  <span class="pe-7s-angle-left-circle"></span>
               </a>
               <?php } ?>
               <div class="collapse navbar-collapse navbar-custom-menu" >
                 <ul class="navbar-nav ml-auto">
                  
                   
                   <li class="nav-item dropdown dropdown-user">
                   <a class="nav-link d-flex" href="#"  role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                     <i class="pe-7s-user"></i>
                     <div style="color: #f9f9fb;margin-top: 13px;">Welcome <?php echo name; ?></div>
                  </a>
                    
                     <div class="dropdown-menu drop_down">
                          <div class="menus">
                              <a class="dropdown-item" href="profile.php"><i class="fa fa-user"></i> User Profile</a>
                              <a class="dropdown-item" href="../logout.php"><i class="fa fa-sign-out"></i> Log Out</a>
                          </div>
                     </div>
                   </li>
                 </ul>
               </div>
             </nav>
            </header>
         <?php
         include("sidebar.php");
         ?>