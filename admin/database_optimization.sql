-- Database optimization suggestions for better DataTables performance
-- Run these queries to add indexes that will significantly improve query performance

-- Add indexes to the lcr table for better performance
-- These indexes will speed up the WHERE clauses and JOINs used in the report

-- Index for vendor filtering (most common filter)
CREATE INDEX idx_lcr_vendor ON lcr(vendor);

-- Index for destination filtering and searching
CREATE INDEX idx_lcr_destination ON lcr(destination);

-- Composite index for vendor + destination filtering (most efficient for combined filters)
CREATE INDEX idx_lcr_vendor_destination ON lcr(vendor, destination);

-- Index for date sorting
CREATE INDEX idx_lcr_date ON lcr(date);

-- Index for code searching
CREATE INDEX idx_lcr_code ON lcr(code);

-- Index for price searching/sorting
CREATE INDEX idx_lcr_price ON lcr(price);

-- Composite index for the most common query pattern (vendor + destination + code)
-- This will speed up the duplicate checking queries in the import scripts
CREATE INDEX idx_lcr_vendor_dest_code ON lcr(vendor, destination, code);

-- Ensure vendors table has proper indexes
CREATE INDEX idx_vendors_name ON vendors(name);

-- Optional: If you have a lot of data, consider partitioning by vendor
-- This is more advanced and should only be done if you have millions of records
-- ALTER TABLE lcr PARTITION BY HASH(vendor) PARTITIONS 10;

-- Performance monitoring query - run this to check index usage
-- SELECT 
--     TABLE_NAME,
--     INDEX_NAME,
--     COLUMN_NAME,
--     CARDINALITY
-- FROM 
--     INFORMATION_SCHEMA.STATISTICS 
-- WHERE 
--     TABLE_SCHEMA = DATABASE() 
--     AND TABLE_NAME IN ('lcr', 'vendors')
-- ORDER BY 
--     TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;
