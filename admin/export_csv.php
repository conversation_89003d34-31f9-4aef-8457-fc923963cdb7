<?php
include_once('../includes/config.php');
include_once('includes/session.php');
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

// Set memory limit and execution time for large exports
ini_set('memory_limit', '512M');
set_time_limit(300); // 5 minutes

// Get filter parameters
$vendor_filter = isset($_GET['vendor']) ? mysqli_real_escape_string($connection, $_GET['vendor']) : '';
$destination_filter = isset($_GET['destination']) ? mysqli_real_escape_string($connection, $_GET['destination']) : '';
$date_from = isset($_GET['date_from']) ? mysqli_real_escape_string($connection, $_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? mysqli_real_escape_string($connection, $_GET['date_to']) : '';

// Build WHERE clause based on filters
$where_conditions = array();

if ($vendor_filter != '') {
    $where_conditions[] = "l.vendor = '$vendor_filter'";
}

if ($destination_filter != '') {
    $where_conditions[] = "l.destination LIKE '%$destination_filter%'";
}

if ($date_from != '') {
    $where_conditions[] = "l.date >= '$date_from'";
}

if ($date_to != '') {
    $where_conditions[] = "l.date <= '$date_to'";
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

// Build the main query with JOIN to get vendor names efficiently
$query = "SELECT 
            l.id,
            COALESCE(v.name, 'Unknown Vendor') as vendor_name,
            l.destination,
            l.code,
            l.price,
            l.date
          FROM lcr l 
          LEFT JOIN vendors v ON l.vendor = v.id 
          $where_clause 
          ORDER BY v.name, l.destination, l.code";

// Execute query
$result = mysqli_query($connection, $query);

if (!$result) {
    die("Database error: " . mysqli_error($connection));
}

// Generate filename with timestamp and filters
$filename_parts = array('vendor_rates');

if ($vendor_filter) {
    $vendor_name_query = "SELECT name FROM vendors WHERE id = '$vendor_filter'";
    $vendor_name_result = mysqli_query($connection, $vendor_name_query);
    if ($vendor_name_result && mysqli_num_rows($vendor_name_result) > 0) {
        $vendor_name = mysqli_fetch_assoc($vendor_name_result)['name'];
        $filename_parts[] = preg_replace('/[^a-zA-Z0-9]/', '_', $vendor_name);
    }
}

if ($destination_filter) {
    $filename_parts[] = preg_replace('/[^a-zA-Z0-9]/', '_', $destination_filter);
}

if ($date_from || $date_to) {
    $date_range = '';
    if ($date_from) $date_range .= $date_from;
    if ($date_from && $date_to) $date_range .= '_to_';
    if ($date_to) $date_range .= $date_to;
    $filename_parts[] = $date_range;
}

$filename_parts[] = date('Y-m-d_H-i-s');
$filename = implode('_', $filename_parts) . '.csv';

// Set headers for CSV download
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

// Create output stream
$output = fopen('php://output', 'w');

// Add UTF-8 BOM for Excel compatibility
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Write CSV headers
$headers = array(
    'ID',
    'Vendor Name',
    'Destination',
    'Code',
    'Rate (USD)',
    'Effective Date',
    'Export Date'
);

fputcsv($output, $headers);

// Write data rows
$row_count = 0;
$export_date = date('Y-m-d H:i:s');

while ($row = mysqli_fetch_assoc($result)) {
    $csv_row = array(
        $row['id'],
        $row['vendor_name'],
        $row['destination'],
        $row['code'],
        '$' . number_format($row['price'], 4), // Format price with 4 decimal places
        $general->dateToRead($row['date']),
        $export_date
    );
    
    fputcsv($output, $csv_row);
    $row_count++;
    
    // Flush output buffer every 1000 rows for large exports
    if ($row_count % 1000 == 0) {
        ob_flush();
        flush();
    }
}

// Add summary row at the end
fputcsv($output, array()); // Empty row
fputcsv($output, array(
    'EXPORT SUMMARY',
    '',
    '',
    '',
    '',
    '',
    ''
));
fputcsv($output, array(
    'Total Records Exported:',
    $row_count,
    '',
    '',
    '',
    '',
    ''
));
fputcsv($output, array(
    'Export Generated:',
    $export_date,
    '',
    '',
    '',
    '',
    ''
));

// Add filter information if any filters were applied
if (!empty($where_conditions)) {
    fputcsv($output, array()); // Empty row
    fputcsv($output, array(
        'FILTERS APPLIED',
        '',
        '',
        '',
        '',
        '',
        ''
    ));
    
    if ($vendor_filter) {
        $vendor_name_query = "SELECT name FROM vendors WHERE id = '$vendor_filter'";
        $vendor_name_result = mysqli_query($connection, $vendor_name_query);
        $vendor_name = 'Unknown';
        if ($vendor_name_result && mysqli_num_rows($vendor_name_result) > 0) {
            $vendor_name = mysqli_fetch_assoc($vendor_name_result)['name'];
        }
        fputcsv($output, array('Vendor Filter:', $vendor_name, '', '', '', '', ''));
    }
    
    if ($destination_filter) {
        fputcsv($output, array('Destination Filter:', $destination_filter, '', '', '', '', ''));
    }
    
    if ($date_from) {
        fputcsv($output, array('Date From:', $date_from, '', '', '', '', ''));
    }
    
    if ($date_to) {
        fputcsv($output, array('Date To:', $date_to, '', '', '', '', ''));
    }
}

fclose($output);

// Log the export activity (optional)
$log_query = "INSERT INTO export_log (user_id, export_type, filename, record_count, filters, created_at) 
              VALUES ('" . userid . "', 'vendor_rates_csv', '$filename', '$row_count', '" . 
              mysqli_real_escape_string($connection, json_encode($_GET)) . "', NOW())";

// Only execute if the export_log table exists
$table_check = mysqli_query($connection, "SHOW TABLES LIKE 'export_log'");
if (mysqli_num_rows($table_check) > 0) {
    mysqli_query($connection, $log_query);
}

exit;
?>
