<?php
$page = "Users";
$back = "dashboard.php";
if (isset($_GET['action']))
    $back = "users.php";
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);
if(type != 1)
{
    echo '<meta http-equiv="refresh" content="0;url=dashboard.php" />';
    exit; 
}
if (isset($_SESSION['msg']) && $_SESSION['msg'] != '') {
    $message = $_SESSION['msg'];
    unset($_SESSION['msg']);
  }
if(isset($_GET['action']))
    {
        if($_GET['action'] == "add")
        {
            $form = "add";
        }
        elseif($_GET['action'] == "edit")
        {
            $form = "edit";
            $id       = mysqli_real_escape_string($connection,$_GET['id']);
            $strSQL = mysqli_query($connection,"select * from users where id='".$id."'");
            if(mysqli_num_rows($strSQL)>0)
            {
                $results    = mysqli_fetch_array($strSQL);
                // print_r($results);
                $name      = $results['name'];
                $email      = $results['email'];
                $status     = $results['status'];
                $type       = $results['type'];
                $phone       = $results['phone'];
                $username       = $results['username'];
                

                
            }
            else
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Invalid user
                </div>';
                $_SESSION['msg'] = $message;
                    echo '<meta http-equiv="refresh" content="0;url=users.php" />';
                    exit; 
            }
        }
        elseif($_GET['action'] == "delete")
        {
            $id       = mysqli_real_escape_string($connection,$_GET['id']);
            $strSQL = mysqli_query($connection,"select id from users where id='".$id."'");
            if(mysqli_num_rows($strSQL)>0)
            {
                $strSQL = mysqli_query($connection,"delete from users where id='".$id."'");
                $message ='<div class="alert alert-danger" role="alert">
                    User deleted
                </div>'; 
                $_SESSION['msg'] = $message;
                    echo '<meta http-equiv="refresh" content="0;url=users.php" />';
                    exit;    
            }
            else
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Invalid user
                </div>';
                $_SESSION['msg'] = $message;
                    echo '<meta http-equiv="refresh" content="0;url=users.php" />';
                    exit; 
            }    
        }
    }
    if(isset($_POST['action']))
    {
        if($_POST['action'] == "add")
        {
            
            $name       = mysqli_real_escape_string($connection,$_POST['name']);
            $email       = mysqli_real_escape_string($connection,$_POST['email']);
            $phone       = mysqli_real_escape_string($connection,$_POST['phone']);
            $type       = mysqli_real_escape_string($connection,$_POST['type']);
            $status      = mysqli_real_escape_string($connection,$_POST['status']);
            $password      = mysqli_real_escape_string($connection,$_POST['password']);
            $username      = mysqli_real_escape_string($connection,$_POST['username']);
            
            // $passwordRW  = substr(md5(microtime()),rand(0,26),6);
            $passwordRW  = $password;
            $password    = password_hash($passwordRW, PASSWORD_DEFAULT);

            $query = "SELECT email FROM `users` where email='".$email."'";
            $result = mysqli_query($connection,$query);
            $numResults = mysqli_num_rows($result);

            if(!filter_var($email, FILTER_VALIDATE_EMAIL)) // Validate email address
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Invalid email address please type a valid email
                </div>';
                $form = "add";
            }
            elseif($numResults>0)
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Account already exist
                </div>';
                $form = "add";
            }
            else
            {
                
                mysqli_query($connection,"insert into `users`(`name`,`email`,`password`,`status`,`phone`,`type`,`username`) values('".$name."','".$email."','".$password."','".$status."','".$phone."','".$type."','".$username."')");
                $id = mysqli_insert_id($connection);
                
                $message ='<div class="alert alert-success" role="alert">
                    User Added
                </div>';
                $id = mysqli_insert_id($connection);                
                $_SESSION['msg'] = $message;
                    echo '<meta http-equiv="refresh" content="0;url=users.php" />';
                    exit; 
            }
        }
        if($_POST['action'] == "edit")
        {
            $error = false;
            // print_r($_POST);
            $name       = mysqli_real_escape_string($connection,$_POST['name']);
            $email       = mysqli_real_escape_string($connection,$_POST['email']);
            $type       = mysqli_real_escape_string($connection,$_POST['type']);
            $phone       = mysqli_real_escape_string($connection,$_POST['phone']);
            $status      = mysqli_real_escape_string($connection,$_POST['status']);
            $username      = mysqli_real_escape_string($connection,$_POST['username']);
            
            

            $id     = mysqli_real_escape_string($connection,$_POST['id']);
            
            $query = "SELECT email FROM `users` where id='".$id."'";
            // echo $query;
            $result = mysqli_query($connection,$query);
            $results = mysqli_fetch_array($result);
            if(mysqli_num_rows($result) > 0)
            {
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) // Validate email address
                {
                    $message ='<div class="alert alert-danger" role="alert">
                        Invalid email address please type a valid email
                    </div>';
                    $form = "add";
                    $error = true;
                }
                elseif($results['email'] != $email) 
                {
                    $query = "SELECT email FROM `users` where email='".$email."'";
                    $result = mysqli_query($connection,$query);
                    {
                        $numResults = mysqli_num_rows($result);
                        if($numResults>0)
                        {
                            $email = $results['email'];
                            $message ='<div class="alert alert-danger" role="alert">
                                Email address already exist
                            </div>';
                            $form = "edit";
                            $error = true;
                        }
                    }
                }
                if($error == false)
                {
                    $pass = "";
                    if(isset($_POST['password']) && $_POST['password'] != "")
                    {
                        $password      = mysqli_real_escape_string($connection,$_POST['password']);
                        $passwordRW  = $password;
                        $password    = password_hash($passwordRW, PASSWORD_DEFAULT);
                        $pass = ",password='$password'";
                    }
                    $query = "update `users` set name='$name', email='$email', status='$status', type='$type', phone='$phone',username='$username' $pass where id='".$id."'";
                    // echo $query;
                    $result = mysqli_query($connection,$query);
                    $message ='<div class="alert alert-success" role="alert">
                        Account updated
                    </div>';
                    $_SESSION['msg'] = $message;
                    echo '<meta http-equiv="refresh" content="0;url=users.php" />';
                    exit;   
                }
            }
            else
            {
                $message ='<div class="alert alert-danger" role="alert">
                    Error occured please try again
                </div>';
                $_SESSION['msg'] = $message;
                    echo '<meta http-equiv="refresh" content="0;url=users.php" />';
                    exit; 
            }
        }
    }
?>
<style>
    .form-control {
    width: 100%;
    }
</style>
<!-- page content -->
<div class="content-wrapper">
            <section class="content-header">
               <div class="header-icon">
                  <i class="fa fa-user-plus"></i>
               </div>
               <div class="header-title">
                  <h1>Users management</h1>
                  <small>Create new user accounts or update existing ones.</small>
               </div>
            </section>
            <section class="content">
               



    <div class="row">
        <div class="col-md-12 grid-margin">
            <div class="d-flex justify-content-between flex-wrap">
                <div class="d-flex justify-content-between align-items-end flex-wrap">
                  <a href="?action=add"  class="btn bb-bg btn-primary bb-bg mt-2 mt-xl-0"><i class="fa fa-plus"></i> Add User</a>
                </div>
            </div>
        </div>
    </div>
    <br>
    <?php echo isset($message) ? $message : ''; //echo $general->myJWT();?>
    <div class="row">
        <div class="col-lg-12 grid-margin stretch-card">
            <div class="card card2">
                <div class="card-body">
                <?php if(isset($form)) {?>
                    <div class="container">
                <form id="CertForm" action="users.php" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                    <div class="row">
                        
                    
                  <div class="form-group col-lg-6">
                    Name: <input type="text" name="name" maxlength="50" value="<?php echo isset($name) ? $name : ''; ?>" class="form-control form-control-lg" placeholder="Name" required="required">
                    <div class="invalid-feedback">Please enter Name</div>
                  </div>
                  <div class="form-group  col-lg-6">
                    Username: <input type="text" name="username"  maxlength="50" value="<?php echo isset($username) ? $username : ''; ?>" class="form-control form-control-lg" placeholder="Username" required="required">
                    <div class="invalid-feedback">Please enter Username</div>
                  </div>
                  <div class="form-group col-lg-6">
                      Phone: <input type="number" name="phone" oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);"  maxlength="10" value="<?php echo isset($phone) ? $phone : ''; ?>" class="form-control form-control-lg" placeholder="Phone" required="required">
                      <div class="invalid-feedback">Please enter Phone</div>
                    </div>
                    <div class="form-group col-lg-6">
                      Email: <input type="email" name="email"  maxlength="50" value="<?php echo isset($email) ? $email : ''; ?>" class="form-control form-control-lg" placeholder="Email" required="required">
                      <div class="invalid-feedback">Please enter Email</div>
                    </div>
                  
                  <div class="form-group col-lg-12">
                    Password: <input type="password" name="password"  class="form-control form-control-lg" placeholder="Password" <?php if($form != "edit"){ ?>required="required"<?php } ?>>
                    <div class="invalid-feedback">Please enter Password</div>
                  </div>
                  <div class="form-group col-lg-6">
                        <label>Account Type:</label>
                            <select name="type" class="form-control" required="required">
                                <option value="">Account Type </option>
                                <option <?php echo (isset($type) && $type == "1") ? 'selected="selected"' : ''; ?> value="1">Admin</option>
                                <option <?php echo (isset($type) && $type == "2") ? 'selected="selected"' : ''; ?> value="2">Project Manager</option>
                                <option <?php echo (isset($type) && $type == "4") ? 'selected="selected"' : ''; ?> value="4">Project Engineer</option>
                                <option <?php echo (isset($type) && $type == "3") ? 'selected="selected"' : ''; ?> value="3">Supervisor/Leading Hand</option>
                            </select>
                            <div class="invalid-feedback">Please select a Account Type</div>
                        </div>
                  <div class="form-group col-lg-6">
                                 <label>Account Status:</label>
                                 <select name="status" class="form-control" required="required">
                                 <option value="">Account Status </option>
                          <option <?php echo (isset($status) && $status == "1") ? 'selected="selected"' : ''; ?> value="1">Active</option>
                          <option <?php echo (isset($status) && $status == "2") ? 'selected="selected"' : ''; ?> value="2">Inactive</option>
                                 </select>
                                 <div class="invalid-feedback">Please select Account status</div>
                              </div>
                  <?php if($form == "edit"){ ?>
                          <input type="hidden" name="id" value="<?php echo $id; ?>">
                          <?php } ?>
                  <div class="mt-3 col-lg-12">
                  <input type="hidden" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">
                    <button type="submit" id="btnSubmit" class="btn btn-block bb-bg btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                  </div>
                  </div>
                </form>
              </div>
                <?php }else{ ?>
                    <div class="table-responsive">
                        <table class="table table2" id="myTable">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody>
                            <?php
                        $query = "SELECT id,`name`,email,phone,IF(`status`=2,'Inactive','Active') AS `status`, `type`,username FROM users";
                        // echo $query;
                        $result = mysqli_query($connection,$query);
                        $id = 1;
                        while($rows=mysqli_fetch_array($result))
                        {
                            
                            if($rows['id'] == 1 || userid == $rows['id'])
                            {
                                $action = "<a href='?action=edit&id=".$rows['id']."' class='label-info label label-default'><i class='fa fa-pencil'></i> Edit </a>";
                            }
                            else
                            {
                                $action = "<a href='?action=edit&id=".$rows['id']."' class='label-info label label-default'><i class='fa fa-pencil'></i> Edit </a> &nbsp; 
                                <a href='?action=delete&id=".$rows['id']."' onclick=\"return confirm('Are you sure you want to DELETE this User?');\" class='label-danger label label-default'><i class='fa fa-trash-o'></i> Delete </a>";
                            }
                            if($rows['type'] == 1)
                            {
                                $accountType = "Admin";
                            }
                            if($rows['type'] == 2)
                            {
                                $accountType = "Project Manager";
                            }
                            if($rows['type'] == 3)
                            {
                                $accountType = "Supervisor/Leading Hand";
                            }
                            if($rows['type'] == 4)
                            {
                                $accountType = "Project Engineer";
                            }
                            $expiredAccount = "";
                            
                            
                            echo "<tr>
                                    <td>".$rows['name']."</td>
                                    <td>".$rows['username']."</td>
                                    <td>".$rows['email']."</td>
                                    <td>".$rows['phone']."</td>
                                    <td>".$accountType."</td>
                                    <td>".$rows['status']."</td>
                                    <td>
                                        $action
                                    </td>
                                    
                                  </tr>";
                            $id++;
                        }
                        ?>
                                
                            </tbody>
                        </table>
                    </div>
                    <?php } ?>
                </div>
            </div>
        </div>
    </div>

            </section>
</div>
<!-- /page content -->
<?php
include("includes/footer.php");
?>
<script>
    $(function () {
    $("#btnSubmit").on("click", function (e) {
        var form = $("#CertForm")[0];
        var isValid = form.checkValidity();
        
        if (!isValid) {
            e.preventDefault();
            e.stopPropagation();
        }
        else
        {
            form.submit();
        }
        form.classList.add('was-validated');
        return false; // For testing only to stay on this page
    });
});
</script>