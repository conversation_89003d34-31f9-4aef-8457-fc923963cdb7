<?php
include_once('../includes/config.php');
include_once('includes/session.php');
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

// Check if this is a valid DataTables request
if (!isset($_POST['draw'])) {
    http_response_code(400);
    echo json_encode(array('error' => 'Invalid request'));
    exit;
}

// DataTables server-side processing
$draw = intval($_POST['draw']);
$start = intval($_POST['start']);
$length = intval($_POST['length']);
$search_value = mysqli_real_escape_string($connection, $_POST['search']['value']);

// Get filter parameters
$vendor = isset($_POST['vendor']) ? mysqli_real_escape_string($connection, $_POST['vendor']) : '';
$filter = isset($_POST['filter']) ? mysqli_real_escape_string($connection, $_POST['filter']) : '';

// Base query with JOIN to avoid N+1 queries
$base_query = "FROM `lcr` l LEFT JOIN `vendors` v ON l.vendor = v.id";

// Build WHERE clause
$where_conditions = array();

// Apply filters from form
if ($vendor != '') {
    $where_conditions[] = "l.vendor = '$vendor'";
}

if ($filter != '') {
    $where_conditions[] = "l.destination LIKE '%$filter%'";
}

// Apply DataTables search
if ($search_value != '') {
    $search_conditions = array(
        "l.destination LIKE '%$search_value%'",
        "v.name LIKE '%$search_value%'",
        "l.code LIKE '%$search_value%'",
        "l.price LIKE '%$search_value%'"
    );
    $where_conditions[] = "(" . implode(" OR ", $search_conditions) . ")";
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

// Count total records (without filtering)
$total_query = "SELECT COUNT(*) as total FROM `lcr` l LEFT JOIN `vendors` v ON l.vendor = v.id";
if ($vendor != '' || $filter != '') {
    $filter_conditions = array();
    if ($vendor != '') $filter_conditions[] = "l.vendor = '$vendor'";
    if ($filter != '') $filter_conditions[] = "l.destination LIKE '%$filter%'";
    $total_query .= " WHERE " . implode(" AND ", $filter_conditions);
}

$total_result = mysqli_query($connection, $total_query);
if (!$total_result) {
    http_response_code(500);
    echo json_encode(array('error' => 'Database error: ' . mysqli_error($connection)));
    exit;
}
$total_records = mysqli_fetch_assoc($total_result)['total'];

// Count filtered records
$filtered_query = "SELECT COUNT(*) as filtered $base_query $where_clause";
$filtered_result = mysqli_query($connection, $filtered_query);
if (!$filtered_result) {
    http_response_code(500);
    echo json_encode(array('error' => 'Database error: ' . mysqli_error($connection)));
    exit;
}
$filtered_records = mysqli_fetch_assoc($filtered_result)['filtered'];

// Handle ordering
$order_column = '';
$order_dir = 'ASC';

if (isset($_POST['order'][0]['column'])) {
    $order_column_index = intval($_POST['order'][0]['column']);
    $order_dir = $_POST['order'][0]['dir'] === 'desc' ? 'DESC' : 'ASC';
    
    switch ($order_column_index) {
        case 0:
            $order_column = 'l.destination';
            break;
        case 1:
            $order_column = 'v.name';
            break;
        case 2:
            $order_column = 'l.code';
            break;
        case 3:
            $order_column = 'l.price';
            break;
        case 4:
            $order_column = 'l.date';
            break;
        default:
            $order_column = 'l.vendor';
            break;
    }
}

$order_clause = '';
if ($order_column != '') {
    $order_clause = "ORDER BY $order_column $order_dir";
} else {
    $order_clause = "ORDER BY l.vendor";
}

// Main query to get data
$data_query = "SELECT l.id, l.destination, v.name as vendor_name, l.code, l.price, l.date 
               $base_query 
               $where_clause 
               $order_clause 
               LIMIT $start, $length";

$data_result = mysqli_query($connection, $data_query);
if (!$data_result) {
    http_response_code(500);
    echo json_encode(array('error' => 'Database error: ' . mysqli_error($connection)));
    exit;
}

$data = array();
while ($row = mysqli_fetch_assoc($data_result)) {
    // Handle long codes with truncation
    $code_display = $row['code'];
    if (strlen($row['code']) > 30) {
        $code_display = "<span id='desc_" . $row['id'] . "' data-full-text='" . htmlspecialchars($row['code']) . "'>" . 
                       htmlspecialchars($row['code']) . "</span> 
                       <span class=\"more-link\" onclick=\"toggleContent('desc_" . $row['id'] . "', this)\">See more</span>";
    } else {
        $code_display = "<span id='desc_" . $row['id'] . "' data-full-text='" . htmlspecialchars($row['code']) . "'>" . 
                       htmlspecialchars($row['code']) . "</span>";
    }
    
    $data[] = array(
        htmlspecialchars($row['destination']),
        htmlspecialchars($row['vendor_name'] ?: 'Unknown'),
        $code_display,
        '$' . number_format($row['price'], 2),
        $general->dateToRead($row['date'])
    );
}

// Prepare response
$response = array(
    "draw" => $draw,
    "recordsTotal" => $total_records,
    "recordsFiltered" => $filtered_records,
    "data" => $data
);

header('Content-Type: application/json');
echo json_encode($response);
?>
