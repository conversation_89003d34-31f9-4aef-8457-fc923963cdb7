<?php
$page = "Export Vendor Rates";
$link = "export_rates.php";
$back = "dashboard.php";
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_SESSION["msg"]) && $_SESSION["msg"] != "") {
    $message = $_SESSION["msg"];
    unset($_SESSION["msg"]);
}

// Handle CSV export request
if (isset($_POST['export_csv'])) {
    $vendor_filter = isset($_POST['vendor_filter']) ? mysqli_real_escape_string($connection, $_POST['vendor_filter']) : '';
    $destination_filter = isset($_POST['destination_filter']) ? mysqli_real_escape_string($connection, $_POST['destination_filter']) : '';
    $date_from = isset($_POST['date_from']) ? mysqli_real_escape_string($connection, $_POST['date_from']) : '';
    $date_to = isset($_POST['date_to']) ? mysqli_real_escape_string($connection, $_POST['date_to']) : '';
    
    // Redirect to CSV generation script
    $params = array();
    if ($vendor_filter) $params[] = "vendor=" . urlencode($vendor_filter);
    if ($destination_filter) $params[] = "destination=" . urlencode($destination_filter);
    if ($date_from) $params[] = "date_from=" . urlencode($date_from);
    if ($date_to) $params[] = "date_to=" . urlencode($date_to);
    
    $query_string = !empty($params) ? '?' . implode('&', $params) : '';
    header("Location: export_csv.php" . $query_string);
    exit;
}

// Get statistics for display
$total_records_query = "SELECT COUNT(*) as total FROM lcr";
$total_result = mysqli_query($connection, $total_records_query);
$total_records = mysqli_fetch_assoc($total_result)['total'];

$total_vendors_query = "SELECT COUNT(DISTINCT vendor) as total FROM lcr";
$vendors_result = mysqli_query($connection, $total_vendors_query);
$total_vendors = mysqli_fetch_assoc($vendors_result)['total'];

?>

<style>
    .export-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        background: #f9f9f9;
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        margin-bottom: 20px;
    }
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .stats-label {
        font-size: 1rem;
        opacity: 0.9;
    }
    .filter-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .export-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        padding: 12px 30px;
        font-size: 1.1rem;
        font-weight: bold;
        border-radius: 25px;
        transition: all 0.3s ease;
    }
    .export-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
</style>

<!-- page content -->
<div class="content-wrapper">
    <section class="content-header">
        <div class="header-icon">
            <i class="fa fa-download"></i>
        </div>
        <div class="header-title">
            <h1><?php echo $page ?></h1>
            <small>Export all vendor rates to CSV format</small>
        </div>
    </section>
    
    <section class="content">
        <?php echo isset($message) ? $message : ''; ?>
        
        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-md-6">
                <div class="stats-card">
                    <div class="stats-number"><?php echo number_format($total_records); ?></div>
                    <div class="stats-label">Total Rate Records</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stats-card">
                    <div class="stats-number"><?php echo number_format($total_vendors); ?></div>
                    <div class="stats-label">Active Vendors</div>
                </div>
            </div>
        </div>

        <!-- Export Form -->
        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-body">
                        <h4 class="card-title mb-4">
                            <i class="fa fa-filter"></i> Export Filters
                        </h4>
                        
                        <form method="post" class="filter-section">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="vendor_filter">
                                            <i class="fa fa-building"></i> Select Vendor (Optional)
                                        </label>
                                        <select name="vendor_filter" id="vendor_filter" class="form-control">
                                            <option value="">All Vendors</option>
                                            <?php
                                            $vendors_query = "SELECT id, name FROM vendors ORDER BY name";
                                            $vendors_result = mysqli_query($connection, $vendors_query);
                                            while ($vendor = mysqli_fetch_assoc($vendors_result)) {
                                                echo "<option value='" . $vendor['id'] . "'>" . htmlspecialchars($vendor['name']) . "</option>";
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="destination_filter">
                                            <i class="fa fa-globe"></i> Destination Filter (Optional)
                                        </label>
                                        <input type="text" name="destination_filter" id="destination_filter" 
                                               class="form-control" placeholder="e.g., United States, UK, etc.">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="date_from">
                                            <i class="fa fa-calendar"></i> Date From (Optional)
                                        </label>
                                        <input type="date" name="date_from" id="date_from" class="form-control">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="date_to">
                                            <i class="fa fa-calendar"></i> Date To (Optional)
                                        </label>
                                        <input type="date" name="date_to" id="date_to" class="form-control">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-12 text-center">
                                    <button type="submit" name="export_csv" class="btn btn-success export-btn">
                                        <i class="fa fa-download"></i> Export to CSV
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Export Information -->
        <div class="row">
            <div class="col-lg-12">
                <div class="export-card">
                    <h5><i class="fa fa-info-circle"></i> Export Information</h5>
                    <ul class="mb-0">
                        <li><strong>File Format:</strong> CSV (Comma Separated Values)</li>
                        <li><strong>Encoding:</strong> UTF-8 with BOM for Excel compatibility</li>
                        <li><strong>Columns Included:</strong> Vendor Name, Destination, Code, Rate (USD), Effective Date</li>
                        <li><strong>File Size:</strong> Approximately <?php echo number_format($total_records * 0.1); ?>KB for all records</li>
                        <li><strong>Processing Time:</strong> Large exports may take 30-60 seconds</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
</div>

<?php include("includes/footer.php"); ?>

<script>
$(document).ready(function() {
    // Add loading state to export button
    $('form').on('submit', function() {
        const btn = $('.export-btn');
        btn.html('<i class="fa fa-spinner fa-spin"></i> Generating CSV...');
        btn.prop('disabled', true);
    });
    
    // Auto-validate date range
    $('#date_from, #date_to').on('change', function() {
        const dateFrom = $('#date_from').val();
        const dateTo = $('#date_to').val();
        
        if (dateFrom && dateTo && dateFrom > dateTo) {
            alert('Date From cannot be later than Date To');
            $(this).val('');
        }
    });
});
</script>
