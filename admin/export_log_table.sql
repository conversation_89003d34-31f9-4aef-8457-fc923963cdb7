-- Optional: Create export log table to track CSV exports
-- This table helps monitor export usage and performance

CREATE TABLE IF NOT EXISTS `export_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `export_type` varchar(50) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `record_count` int(11) NOT NULL DEFAULT 0,
  `filters` text,
  `file_size` bigint(20) DEFAULT NULL,
  `execution_time` decimal(10,3) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_export_type` (`export_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Sample query to view export statistics
-- SELECT 
--     export_type,
--     COUNT(*) as total_exports,
--     SUM(record_count) as total_records_exported,
--     AVG(record_count) as avg_records_per_export,
--     MAX(created_at) as last_export
-- FROM export_log 
-- WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
-- GROUP BY export_type
-- ORDER BY total_exports DESC;
