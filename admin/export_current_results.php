<?php
include_once('../includes/config.php');
include_once('includes/session.php');
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

// Set memory limit and execution time
ini_set('memory_limit', '256M');
set_time_limit(120);

// Get the same filter parameters as the report page
$vendor = isset($_GET['vendor']) ? mysqli_real_escape_string($connection, $_GET['vendor']) : '';
$filter = isset($_GET['filter']) ? mysqli_real_escape_string($connection, $_GET['filter']) : '';

// Build the same query as used in the report page
$where_conditions = array();

if ($vendor != '') {
    $where_conditions[] = "l.vendor = '$vendor'";
}

if ($filter != '') {
    $where_conditions[] = "l.destination LIKE '%$filter%'";
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

// Query to get the filtered results (same as report page logic)
$query = "SELECT 
            l.id,
            l.destination,
            COALESCE(v.name, 'Unknown Vendor') as vendor_name,
            l.code,
            l.price,
            l.date
          FROM lcr l 
          LEFT JOIN vendors v ON l.vendor = v.id 
          $where_clause 
          ORDER BY l.vendor, l.destination";

$result = mysqli_query($connection, $query);

if (!$result) {
    die("Database error: " . mysqli_error($connection));
}

// Generate filename
$filename_parts = array('search_results');

if ($vendor) {
    $vendor_name_query = "SELECT name FROM vendors WHERE id = '$vendor'";
    $vendor_name_result = mysqli_query($connection, $vendor_name_query);
    if ($vendor_name_result && mysqli_num_rows($vendor_name_result) > 0) {
        $vendor_name = mysqli_fetch_assoc($vendor_name_result)['name'];
        $filename_parts[] = preg_replace('/[^a-zA-Z0-9]/', '_', $vendor_name);
    }
}

if ($filter) {
    $filename_parts[] = preg_replace('/[^a-zA-Z0-9]/', '_', $filter);
}

$filename_parts[] = date('Y-m-d_H-i-s');
$filename = implode('_', $filename_parts) . '.csv';

// Set headers for CSV download
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

// Create output stream
$output = fopen('php://output', 'w');

// Add UTF-8 BOM for Excel compatibility
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Write CSV headers
$headers = array(
    'ID',
    'Destination',
    'Vendor Name',
    'Code',
    'Rate (USD)',
    'Effective Date',
    'Export Date'
);

fputcsv($output, $headers);

// Write data rows
$row_count = 0;
$export_date = date('Y-m-d H:i:s');

while ($row = mysqli_fetch_assoc($result)) {
    $csv_row = array(
        $row['id'],
        $row['destination'],
        $row['vendor_name'],
        $row['code'],
        '$' . number_format($row['price'], 4),
        $general->dateToRead($row['date']),
        $export_date
    );
    
    fputcsv($output, $csv_row);
    $row_count++;
}

// Add summary
fputcsv($output, array()); // Empty row
fputcsv($output, array(
    'SEARCH RESULTS EXPORT',
    '',
    '',
    '',
    '',
    '',
    ''
));
fputcsv($output, array(
    'Total Records:',
    $row_count,
    '',
    '',
    '',
    '',
    ''
));
fputcsv($output, array(
    'Export Date:',
    $export_date,
    '',
    '',
    '',
    '',
    ''
));

// Add search criteria
if ($vendor || $filter) {
    fputcsv($output, array()); // Empty row
    fputcsv($output, array(
        'SEARCH CRITERIA',
        '',
        '',
        '',
        '',
        '',
        ''
    ));
    
    if ($vendor) {
        $vendor_name_query = "SELECT name FROM vendors WHERE id = '$vendor'";
        $vendor_name_result = mysqli_query($connection, $vendor_name_query);
        $vendor_name = 'Unknown';
        if ($vendor_name_result && mysqli_num_rows($vendor_name_result) > 0) {
            $vendor_name = mysqli_fetch_assoc($vendor_name_result)['name'];
        }
        fputcsv($output, array('Vendor:', $vendor_name, '', '', '', '', ''));
    }
    
    if ($filter) {
        fputcsv($output, array('Destination Filter:', $filter, '', '', '', '', ''));
    }
}

fclose($output);
exit;
?>
