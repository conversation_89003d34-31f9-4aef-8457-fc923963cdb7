<?php
// Performance testing script for the report page
// This script helps measure the performance improvements

include_once('../includes/config.php');
include_once('includes/session.php');
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

echo "<h2>Performance Test Results</h2>";
echo "<p>Testing database query performance for the report page...</p>";

// Test parameters
$test_vendor = 1; // Change this to a vendor ID that exists in your database
$test_filter = 'United'; // Change this to a destination that exists

echo "<h3>Test Parameters:</h3>";
echo "Vendor ID: $test_vendor<br>";
echo "Filter: $test_filter<br><br>";

// Test 1: Old method (N+1 queries)
echo "<h3>Test 1: Old Method (N+1 Queries)</h3>";
$start_time = microtime(true);

$old_query = "SELECT * FROM `lcr` WHERE vendor = $test_vendor AND destination LIKE '%$test_filter%' ORDER BY vendor LIMIT 30";
$old_result = mysqli_query($connection, $old_query);
$old_count = 0;

while ($row = mysqli_fetch_array($old_result)) {
    // Simulate the N+1 query problem
    $vendor_name = $general->getVendorNameByID($row['vendor']);
    $formatted_date = $general->dateToRead($row['date']);
    $old_count++;
}

$old_time = microtime(true) - $start_time;
echo "Records processed: $old_count<br>";
echo "Time taken: " . number_format($old_time * 1000, 2) . " ms<br><br>";

// Test 2: New method (JOIN query)
echo "<h3>Test 2: New Method (JOIN Query)</h3>";
$start_time = microtime(true);

$new_query = "SELECT l.id, l.destination, v.name as vendor_name, l.code, l.price, l.date 
              FROM `lcr` l 
              LEFT JOIN `vendors` v ON l.vendor = v.id 
              WHERE l.vendor = $test_vendor AND l.destination LIKE '%$test_filter%' 
              ORDER BY l.vendor 
              LIMIT 30";

$new_result = mysqli_query($connection, $new_query);
$new_count = 0;

while ($row = mysqli_fetch_assoc($new_result)) {
    // No additional queries needed - all data is in the result
    $formatted_date = $general->dateToRead($row['date']);
    $new_count++;
}

$new_time = microtime(true) - $start_time;
echo "Records processed: $new_count<br>";
echo "Time taken: " . number_format($new_time * 1000, 2) . " ms<br><br>";

// Performance comparison
$improvement = (($old_time - $new_time) / $old_time) * 100;
echo "<h3>Performance Improvement:</h3>";
echo "Speed improvement: " . number_format($improvement, 1) . "%<br>";
echo "Time saved: " . number_format(($old_time - $new_time) * 1000, 2) . " ms<br><br>";

// Database statistics
echo "<h3>Database Statistics:</h3>";
$lcr_count = mysqli_fetch_assoc(mysqli_query($connection, "SELECT COUNT(*) as count FROM lcr"))['count'];
$vendor_count = mysqli_fetch_assoc(mysqli_query($connection, "SELECT COUNT(*) as count FROM vendors"))['count'];

echo "Total LCR records: " . number_format($lcr_count) . "<br>";
echo "Total vendors: " . number_format($vendor_count) . "<br><br>";

// Index recommendations
echo "<h3>Index Status Check:</h3>";
$index_query = "SHOW INDEX FROM lcr";
$index_result = mysqli_query($connection, $index_query);
$indexes = array();

while ($index = mysqli_fetch_assoc($index_result)) {
    $indexes[] = $index['Key_name'] . ' (' . $index['Column_name'] . ')';
}

echo "Current indexes on LCR table:<br>";
if (empty($indexes)) {
    echo "<span style='color: red;'>No indexes found! Performance will be poor.</span><br>";
    echo "Please run the queries in database_optimization.sql to add indexes.<br>";
} else {
    foreach ($indexes as $index) {
        echo "- $index<br>";
    }
}

echo "<br><h3>Recommendations:</h3>";
echo "1. Run the SQL queries in 'database_optimization.sql' to add proper indexes<br>";
echo "2. The new server-side processing should show significant performance improvements<br>";
echo "3. Monitor query performance regularly, especially as data grows<br>";
echo "4. Consider implementing caching for frequently accessed vendor names<br>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #333; }
h3 { color: #666; margin-top: 20px; }
</style>
