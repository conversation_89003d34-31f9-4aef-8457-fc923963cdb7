<?php
$page = "Settings";
include("includes/header.php");


if (isset($_POST['action'])) {
  if ($_POST['action'] == "password") {
    $password = mysqli_real_escape_string($connection, $_POST['password']);
    $npassword = mysqli_real_escape_string($connection, $_POST['npassword']);
    $rpassword = mysqli_real_escape_string($connection, $_POST['rpassword']);

    $strSQL = mysqli_query($connection, "select id,password from users where id='" . userid . "'");
    if (mysqli_num_rows($strSQL) > 0) {
      $results = mysqli_fetch_array($strSQL);
      if (password_verify($password, $results['password'])) {
        if ($npassword != $rpassword) {
          $message = '<div class="alert alert-danger alert-dismissible show" role="alert">
                            New password dosen\'t match
                        </div>';
        } else {
          $password     = password_hash(mysqli_real_escape_string($connection, $npassword), PASSWORD_DEFAULT);
          $query = "update users set password='" . $password . "' where id='" . userid . "'";

          mysqli_query($connection, $query);
          $message = '<div class="alert alert-success alert-dismissible show" role="alert">
                                Your password changed sucessfully 
                            </div>';
        }
      } else {
        $message = '<div class="alert alert-danger alert-dismissible show" role="alert">
                            Invalid current password
                        </div>';
      }
    } else {
      $message = '<div class="alert alert-danger alert-dismissible show" role="alert">
                    Internal error please try again
                </div>';
    }
  }
  if ($_POST['action'] == "edit") {
    $name  = mysqli_real_escape_string($connection, $_POST['name']);
    $email  = mysqli_real_escape_string($connection, $_POST['email']);
    $phone  = mysqli_real_escape_string($connection, $_POST['phone']);
    $username  = mysqli_real_escape_string($connection, $_POST['username']);
    


    $query = "update users set name='$name', email='$email', phone='$phone', username='$username' where id='" . userid . "'";
    $result = mysqli_query($connection, $query);
    $message = '<div class="alert alert-success alert-dismissible show" role="alert">
                        Account updated
                    </div>';
  }
}

$strSQL = mysqli_query($connection, "select * from users where id='" . userid . "'");
if (mysqli_num_rows($strSQL) > 0) {
  $results = mysqli_fetch_array($strSQL);
  $name  = $results['name'];
  $email  = $results['email'];
  $phone  = $results['phone'];
  $username  = $results['username'];
  $type  = $results['type'];
  if($results['type'] == 1)
  {
      $role = "Administrator";
  }
  if($results['type'] == 2)
  {
      $role = "Project Manager";
  }
  if($results['type'] == 4)
  {
      $role = "Project Engineer";
  }
  if($results['type'] == 3)
  {
      $role = "Supervisor/Leading Hand";
  }
  
}
?>
<div class="content-wrapper">
            <section class="content-header">
               <div class="header-icon">
                  <i class="fa fa-user"></i>
               </div>
               <div class="header-title">
                  <h1>User Profile</h1>
                  <small>&nbsp;</small>
               </div>
            </section>
            <section class="content">
               
           

  <div class="row">
    <div class="col-md-12">
      <div class="card card2">
        <div class="card-body">
          <?php echo isset($message) ? $message : ''; ?>

          <div class="card card-nav-tabs card-plain">
            <div class="card-header card-header-danger">
              <!-- colors: "header-primary", "header-info", "header-success", "header-warning", "header-danger" -->
              <div class="nav-tabs-navigation">
                <div class="nav-tabs-wrapper">
                  <ul class="nav nav-tabs" data-tabs="tabs">
                    <li class="nav-item">
                      <a class="nav-link <?php echo (isset($_POST['password'])) ? '' : 'active'; ?>" href="#account" data-toggle="tab">Account Info</a>
                    </li>
                    <li class="nav-item <?php echo (isset($_POST['password'])) ? 'active' : ''; ?>">
                      <a class="nav-link" href="#password" data-toggle="tab">Change Password</a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="card-body ">
              <div class="tab-content">
                <div class="tab-pane <?php echo (isset($_POST['password'])) ? '' : 'active'; ?>" id="account">
                  <form id="form" action="" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                    <div class="form-group">
                      <label class="control-label col-md-12 col-sm-12 col-xs-12" for="name">Full Name <span class="required">*</span>
                      </label>
                      <div class="col-md-12 col-sm-12 col-xs-12">
                        <input type="text" value="<?php echo isset($name) ? $name : ''; ?>" name="name" id="name" required="required" class="form-control">
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="control-label col-md-12 col-sm-12 col-xs-12" for="name">Username <span class="required">*</span>
                      </label>
                      <div class="col-md-12 col-sm-12 col-xs-12">
                        <input type="text" value="<?php echo isset($username) ? $username : ''; ?>" name="username" id="username" required="required" class="form-control">
                      </div>
                    </div>
                    <div class="form-group">
                      <label class="control-label col-md-12 col-sm-12 col-xs-12" for="name">Phone Number <span class="required">*</span>
                      </label>
                      <div class="col-md-12 col-sm-12 col-xs-12">
                        <input type="text" value="<?php echo isset($phone) ? $phone : ''; ?>" name="phone" id="phone" required="required" class="form-control">
                      </div>
                    </div>
                    <div class="form-group">
                      <label for="email" class="control-label col-md-12 col-sm-12 col-xs-12">Email <span class="required">*</span></label>
                      <div class="col-md-12 col-sm-12 col-xs-12">
                        <input id="email" value="<?php echo isset($email) ? $email : ''; ?>" class="form-control" required="required" type="email" name="email">
                      </div>
                    </div>
                    <div class="form-group">
                      <label for="email" class="control-label col-md-12 col-sm-12 col-xs-12">Role </label>
                      <div class="col-md-12 col-sm-12 col-xs-12">
                        <?php echo isset($role) ? $role : ''; ?>
                      </div>
                    </div>
                   

                    

                    <div class="form-group">
                      <div class="col-md-6 col-sm-6 col-xs-12 col-md-offset-3">
                        <button type="submit" class="btn btn-success bb-bg" name="action" value="edit"> S A V E </button>
                      </div>
                    </div>
                  </form>
                </div>
                <div class="tab-pane <?php echo (isset($_POST['password'])) ? 'active' : ''; ?>" id="password">
                  <form id="form" action="" method="post" data-parsley-validate class="form-horizontal form-label-left">
                    <div class="form-group">
                      <label for="password" class="control-label col-md-12 col-sm-12 col-xs-12">Current Password <span class="required">*</span></label>
                      <div class="col-md-12 col-sm-12 col-xs-12">
                        <input id="password" class="form-control" required="required" type="password" name="password">
                      </div>
                    </div>
                    <div class="form-group">
                      <label for="npassword" class="control-label col-md-12 col-sm-12 col-xs-12">New Password <span class="required">*</span></label>
                      <div class="col-md-12 col-sm-12 col-xs-12">
                        <input id="npassword" class="form-control" required="required" type="password" name="npassword">
                      </div>
                    </div>
                    <div class="form-group">
                      <label for="rpassword" class="control-label col-md-12 col-sm-12 col-xs-12">Retype New Password <span class="required">*</span></label>
                      <div class="col-md-12 col-sm-12 col-xs-12">
                        <input id="rpassword" class="form-control" required="required" type="password" name="rpassword">
                      </div>
                    </div>
                    <div class="form-group">
                      <div class="col-md-12 col-sm-12 col-xs-12 col-md-offset-3">
                        <button type="submit" class="btn btn-success bb-bg" name="action" value="password"> S A V E </button>
                      </div>
                    </div>
                  </form>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </section>
            
            </div>

  <?php
  include("includes/footer.php");
  ?>