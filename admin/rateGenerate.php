<?php
$page = "Rate Generator";
$link = "rateGenerate.php";
$table = "rateGenerate";
$back = "dashboard.php";
if (isset($_GET["action"]))
    $back = "rateGenerate.php";
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_SESSION["msg"]) && $_SESSION["msg"] != "") {
    $message = $_SESSION["msg"];
    unset($_SESSION["msg"]);
}
if (isset($_GET["action"])) {
    if ($_GET["action"] == "add") {
        $form = "add";
    } elseif ($_GET["action"] == "edit") {
        $form = "edit";
        $id       = mysqli_real_escape_string($connection, $_GET["id"]);
        $strSQL = mysqli_query($connection, "select * from `$table` where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $results    = mysqli_fetch_array($strSQL);
            $name = $results['name'];
            $trunk = $results['trunk'];
            $currency = $results['currency'];
            $account = $results['account_id'];
            $prefix = $results['prefix'];
            
        } else {
            $message = '<div class="alert alert-danger" role="alert">Invalid ' . $page . '</div>';
            $_SESSION["msg"] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        }
    } elseif ($_GET['action'] == "delete") {
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select id from `$table` where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $strSQL = mysqli_query($connection, "delete from `$table` where id='" . $id . "'");

            $message = '<div class="alert alert-danger" role="alert">' . $page . ' deleted</div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        } else {
            $message = '<div class="alert alert-danger" role="alert">Invalid ' . $page . '</div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        }
    }
}
if (isset($_POST['action'])) {
    if ($_POST['action'] == "edit") {
        $name = mysqli_real_escape_string($connection, $_POST['name']);
        $trunk = mysqli_real_escape_string($connection, $_POST['trunk']);
        $currency = mysqli_real_escape_string($connection, $_POST['currency']);
        $prefix = mysqli_real_escape_string($connection, $_POST['prefix']);
        $account_id = mysqli_real_escape_string($connection, $_POST['account']);

        $id     = mysqli_real_escape_string($connection, $_POST['id']);


        $sql = "UPDATE `$table` SET 
`name` = '$name',
`trunk` = '$trunk', 
`currency` = '$currency', 
`prefix` = '$prefix', 
`account_id` = '$account_id' $picture WHERE id='$id'";

        $result = mysqli_query($connection, $sql);

        $message = '<div class="alert alert-success" role="alert">' . $page . ' updated</div>';
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    } else {
        $message = '<div class="alert alert-danger" role="alert">Error occured please try again</div>';
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    }
}

?>
<style>
    .form-control {
        width: 100%;
    }

    .input-group-text {
        padding: 4px 14px 4px 0px;
        width: 50%;
        height: 34px;
    }

    .input-group-prepend {
        margin-right: -10px;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }

    .bootstrap-select.form-control-lg .dropdown-toggle {
        padding: 0.375rem 1rem;
    }
</style>
<!-- page content -->
<div class="content-wrapper">
    <section class="content-header">
        <div class="header-icon">
            <i class="fa fa-edit"></i>
        </div>
        <div class="header-title">
            <h1><?php echo $page ?> Management</h1>
            <small>Add, Update or Delete <?php echo $page ?></small>
        </div>
    </section>
    <section class="content">
        <?php echo isset($message) ? $message : ''; ?> <div class="row">
            <div class="col-lg-12 grid-margin stretch-card">
                <div class="card card2">
                    <div class="card-body">
                        <?php if (isset($form)) { ?> <div class="cont1ainer">
                                <form id="dataForm" action="<?php echo $link; ?>" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                                    <?php if ($form == "edit") { ?>
                                        <input type="hidden" name="id" value="<?php echo $id; ?>">
                                    <?php } ?>
                                    <div class="row">
                                        <div class="form-group col-md-12">
                                            <label for="name">Name:</label>
                                            <input type="text" class="form-control" id="name" name="name" value="<?php echo isset($name) ? $name : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-lg-6">
                                            <label for="name">Account</label>
                                            <select name="account" id="account" class="form-control form-control-lg selectpicker" required>
                                                <option value="">Select  *</option>
                                                <?php
                                                $query = "SELECT id, `fname`,`lname` FROM `account` where uid = '" . userid . "'";
                                                $result = mysqli_query($connection, $query);

                                                while ($rows = mysqli_fetch_array($result)) {
                                                    if (isset($form) && ($form == "edit" || $form == "add") && $account == $rows['id']) {
                                                ?>
                                                        <option selected="selected" value="<?php echo $rows['id']; ?>"><?php echo $rows['fname']; ?> <?php echo $rows['lname']; ?></option>
                                                    <?php
                                                    } else {
                                                    ?>
                                                        <option value="<?php echo $rows['id']; ?>"><?php echo $rows['fname']; ?> <?php echo $rows['lname']; ?></option>
                                                <?php
                                                    }
                                                }
                                                ?>
                                            </select>
                                            <div class="invalid-feedback">Please select a account</div>
                                        </div>


                                        <div class="form-group col-lg-6">
                                            <label for="name">Prefix</label>
                                            <select name="prefix" id="prefix" class="form-control form-control-lg selectpicker" required>
                                                <option value="">Select  *</option>
                                                
                                            </select>
                                            <div class="invalid-feedback">Please select a prefix</div>
                                        </div>


                                        <div class="form-group col-md-6">
                                            <label for="trunk">Trunk:</label>
                                            <input type="text" class="form-control" id="trunk" name="trunk" value="<?php echo isset($trunk) ? $trunk : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        
                                        <div class="form-group col-lg-6">
                                            <label for="name">Currency</label>
                                            <select name="currency" id="currency" class="form-control form-control-lg selectpicker" required>
                                                <option value="">Select  *</option>
                                                <?php
                                                $query = "SELECT id, `code`,`symbol` FROM `currency`";
                                                $result = mysqli_query($connection, $query);

                                                while ($rows = mysqli_fetch_array($result)) {
                                                    if (isset($form) && ($form == "edit" || $form == "add") && $currency == $rows['code']) {
                                                ?>
                                                        <option selected="selected" value="<?php echo $rows['code']; ?>"><?php echo $rows['code']; ?> (<?php echo $rows['symbol']; ?>)</option>
                                                    <?php
                                                    } else {
                                                    ?>
                                                        <option value="<?php echo $rows['code']; ?>"><?php echo $rows['code']; ?> (<?php echo $rows['symbol']; ?>)</option>
                                                <?php
                                                    }
                                                }
                                                ?>
                                            </select>
                                            <div class="invalid-feedback">Please select a currency</div>
                                        </div>
                                        
                                        <div class="mt-3 col-lg-12">
                                            <input type="hidden" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">
                                            <button type="submit" id="btnSubmit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        <?php } else { ?> 
                            <?php $query = "SELECT * FROM `$table`"; ?>
                            <div class="table-responsive">
                                <table class="table table2" id="myTable">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Account</th>
                                            <th>Trunk</th>
                                            <th>Currency</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $result = mysqli_query($connection, $query);
                                        while ($rows = mysqli_fetch_array($result)) {

                                            $action = " <a data-toggle='tooltip' data-placement='top' title='Edit' href='?action=edit&id=" . $rows['id'] . "' class='btn-info btn btn-sm'><i class='fa fa-pencil'></i></a> 
                                            <a data-toggle='tooltip' data-placement='top' href='rateRules.php?action=rules&rate_id=" . $rows['id'] . "' class='btn-success btn btn-sm' title='Add Destination'><i class='fa fa-list-alt'></i></a> ";
                                            $query = "SELECT * FROM `rateRules` where rate_id=" . $rows['id'];
                                            $result2 = mysqli_query($connection, $query);
                                            if (mysqli_num_rows($result2) > 0) {
                                                $action .= "<a data-toggle='tooltip' data-placement='top' target='_blank' href='downloadRateRules.php?action=rules&id=" . $rows['id'] . "' class='btn-primary btn btn-sm' title='Download Rates'><i class='fa fa-download'></i></a> ";
                                            }
                                            $action .= "<a data-toggle='tooltip' data-placement='top' title='Delete' href='?action=delete&id=" . $rows['id'] . "' onclick=\"return confirm('Are you sure you want to DELETE this Record?');\" class='btn-danger btn btn-sm'><i class='fa fa-trash-o'></i></a>";

                                            echo "
                                        <tr><td>" . $rows['name'] . "</td>
                                        <td>" . $general->getAccountNameByID($rows['account_id']) . " (" . $rows['prefix'] . ")</td>
                                            <td>" . $rows['trunk'] . "</td>
                                            <td>" . $rows['currency'] . "</td>

                    <td>$action</td>
                    </tr>";
                                        }
                                        ?></tbody>
                                </table>
                            </div> <?php } ?> </div>
                </div>
            </div>
        </div>

    </section>
</div>
<!-- /page content -->
<?php include("includes/footer.php"); ?><script>
    $(function() {
        $('[data-toggle="tooltip"]').tooltip()
        
        $("#btnSubmit").on("click", function(e) {
            var form = $("#dataForm")[0];
            var isValid = form.checkValidity();

            if (!isValid) {
                e.preventDefault();
                e.stopPropagation();
            } else {
                form.submit();
            }
            form.classList.add('was-validated');
            return false; // For testing only to stay on this page
        });
        // on account dropdown change get prefix list
        $('#account').on('change', function() {
            var id = $(this).val();
            $.ajax({
                url: 'ajaxLoad.php',
                type: 'GET',
                data: {
                    action: 'getPrefixList',
                    id: id
                },
                success: function(data) {
                    var prefix = $('#prefix');
                    prefix.empty();
                    prefix.append('<option value="">Select  *</option>');
                    console.log(data);
                    $.each(data.prefix, function(index, value) {
                        prefix.append('<option value="' + value + '">' + value + '</option>');
                    });
                }
            });
        });
        <?php if ($form == "edit") { ?>
        
            var id = <?php echo $account; ?>;
            $.ajax({
                url: 'ajaxLoad.php',
                type: 'GET',
                data: {
                    action: 'getPrefixList',
                    id: id
                },
                success: function(data) {
                    var prefix = $('#prefix');
                    prefix.empty();
                    prefix.append('<option value="">Select  *</option>');
                    console.log(data);
                    $.each(data.prefix, function(index, value) {
                        prefix.append('<option value="' + value + '">' + value + '</option>');
                    });
                    prefix.val('<?php echo $prefix; ?>');
                }
            });
        <?php } ?>
    });
</script>