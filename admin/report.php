<?php
$page = "View Report";
$link = "report.php";
$back = "dashboard.php";
if (isset($_GET["action"]))
    $back = "report.php";
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_SESSION["msg"]) && $_SESSION["msg"] != "") {
    $message = $_SESSION["msg"];
    unset($_SESSION["msg"]);
}

?>
<link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
<style>
    .ui-autocomplete {
        max-height: 300px;
        overflow-y: auto;
        /* prevent horizontal scrollbar */
        overflow-x: hidden;
    }

    /* IE 6 doesn't support max-height
   * we use height instead, but this forces the menu to always be this tall
   */
    * html .ui-autocomplete {
        height: 100px;
    }

    .ui-autocomplete-loading {
        background: white url("images/ui-anim_ba1sic_16x16.gif") right center no-repeat;
    }

    .ui-autocomplete-loading {
        background: white url("http://ajax.googleapis.com/ajax/libs/jqueryui/1.8.2/themes/smoothness/images/ui-anim_basic_16x16.gif") right center no-repeat;
    }


    .dropdown-menu.show {
        overflow: overlay;
        height: 300px;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }

    .truncate {
            display: inline; /* Show text inline */
        }
        .more-link {
            color: blue;
            cursor: pointer;
        }

        /* DataTables processing indicator styling */
        .dataTables_processing {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 200px;
            margin-left: -100px;
            margin-top: -26px;
            text-align: center;
            padding: 1em 0;
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* Improve table loading appearance */
        #myTableR_wrapper .dataTables_length,
        #myTableR_wrapper .dataTables_filter,
        #myTableR_wrapper .dataTables_info,
        #myTableR_wrapper .dataTables_paginate {
            margin: 10px 0;
        }


</style>
<!-- page content -->
<div class="content-wrapper">
    <section class="content-header">
        <div class="header-icon">
            <i class="fa fa-file-text-o"></i>
        </div>
        <div class="header-title">
            <h1><?php echo $page ?></h1>
            <small>&nbsp;</small>
        </div>
    </section>
    <section class="content">



        <br>
        <?php echo isset($message) ? $message : ''; ?> <div class="row">
            <div class="col-lg-12 grid-margin stretch-card">
                <div class="card card2">
                    <div class="card-body">

                    <?php
                        $wc = '';
                        if (isset($_GET['search']) && $_GET['search'] == "search") {
                            $vendor       = mysqli_real_escape_string($connection, $_GET['vendor']);
                            $filter       = mysqli_real_escape_string($connection, $_GET['filter']);
                            $showRecords = true;
                            
                            
                        }

                        ?>
                        <form action="" method="get" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                            <div class="row">
                                <div class="form-group col-lg-5">
                                    <input autocomplete="off" type="text" class="form-control basicAutoComplete" name="filter" value="<?php echo isset($filter) ? $filter : ''; ?>" aria-describedby="helpId" placeholder="Search">
                                </div>
                                <div class="form-group col-lg-5">
                                    <select name="vendor" id="vendor" class="form-control">
                                        <option value="">Select Vendor</option>
                                        <?php
                                        $queryVendors = "SELECT id, `name` FROM `vendors`";
                                        $result = mysqli_query($connection, $queryVendors);
                                
                                        while ($rows = mysqli_fetch_array($result)) {
                                            if (isset($vendor) && $vendor == $rows['id']) {
                                        ?>
                                                <option selected="selected" value="<?php echo $rows['id']; ?>"><?php echo $rows['name']; ?></option>
                                            <?php
                                            } else {
                                            ?>
                                                <option value="<?php echo $rows['id']; ?>"><?php echo $rows['name']; ?></option>
                                        <?php
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>

                                <div class="form-group col-lg-1">
                                    <button class="btn btn-sm btn-primary w-100" type="submit" name="search" value="search">Search</button>
                                </div>

                                <div class="form-group col-lg-1">
                                    <a href="export_rates.php" class="btn btn-sm btn-success w-100" title="Export All Rates">
                                        <i class="fa fa-download"></i> Export
                                    </a>
                                </div>

                            </div>
                        </form>
                        
                        <?php
                        if(isset($_GET['search'])){
                            $vendor       = mysqli_real_escape_string($connection, $_GET['vendor']);
                            $filter       = mysqli_real_escape_string($connection, $_GET['filter']);
                            if($vendor != '' && $filter != '')
                            {
                                $query = "SELECT * FROM `lcr` where destination like '%$filter%' and vendor = $vendor order by vendor";
                            }
                            else if($vendor != '' && $filter == '')
                            {
                                $query = "SELECT * FROM `lcr` where vendor = $vendor order by vendor";
                            }
                            else if($vendor == '' && $filter != '')
                            {
                                $query = "SELECT * FROM `lcr` where destination like '%$filter%' order by vendor";
                            }
                        }
                        
                        ?>
                        <?php if (!isset($_GET['search']) || $_GET['search'] != 'search'): ?>
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"></i> Please use the search form above to filter and view LCR data.
                            </div>
                        <?php else: ?>
                            <div class="mb-3">
                                <a href="export_current_results.php?vendor=<?php echo urlencode($vendor); ?>&filter=<?php echo urlencode($filter); ?>"
                                   class="btn btn-success btn-sm">
                                    <i class="fa fa-download"></i> Export Current Search Results
                                </a>
                                <small class="text-muted ml-2">Export the filtered results as CSV</small>
                            </div>
                        <?php endif; ?>

                        <div class="table-responsive">
                                <table class="table table2" id="myTableR" <?php echo (!isset($_GET['search']) || $_GET['search'] != 'search') ? 'style="display:none;"' : ''; ?>>
                                    <thead>
                                        <tr>
                                            <th>Destination</th>
                                            <th>Vendor</th>
                                            <th>Code</th>
                                            <th>Rate (USD)</th>
                                            <th class="sorting sort-date">Effective Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Data will be loaded via AJAX -->
                                    </tbody>
                                </table>
                            </div>
                    </div>
                </div>
            </div>
        </div>

    </section>
</div>
<!-- /page content -->
<?php include("includes/footer.php"); ?>


<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"></script>
<script>
    $(function() {
        $(".basicAutoComplete").autocomplete({
            source: function(request, response) {
                $.ajax({
                    url: "auto.php",
                    dataType: "json",
                    data: {
                        term: request.term
                    },
                    success: function(data) {
                        response(data);
                    }
                });
            },
            minLength: 3,
            select: function(event, ui) {
                // alert( "Selected: " + ui.item.value + " aka " + ui.item.id );
            }
        });
        $.ui.autocomplete.prototype._renderItem = function(ul, item) {
            var t = String(item.value).replace(
                new RegExp(this.term, "gi"),
                "<strong>$&</strong>");
            return $("<li></li>")
                .data("item.autocomplete", item)
                .append("<div>" + t + "</div>")
                .appendTo(ul);
        };
    });
    function toggleContent(id, element) {
        var span = document.getElementById(id);
        var fullText = span.getAttribute('data-full-text');
        
        // If there is no "data-full-text" attribute, store the full text
        if (!fullText) {
            fullText = span.innerText;
            span.setAttribute('data-full-text', fullText);
        }
        
        // If content is currently truncated, show full content
        if (span.innerText.length > 30) {
            span.innerText = fullText; // Show full text
            element.innerText = ''; // Change link text to "See less"
        } else {
            var truncatedText = fullText.substring(0, 30) + '...'; // Show only 30 characters
            span.innerText = truncatedText; 
            element.innerText = 'See more'; // Change link text to "See more"
        }
    }

    // Function to initialize truncated text (called after DataTable draws)
    function initializeTruncatedText() {
        document.querySelectorAll('span[id^="desc"]').forEach(function(span) {
            var fullText = span.getAttribute('data-full-text') || span.innerText;
            span.setAttribute('data-full-text', fullText);
            if (fullText.length > 30) {
                span.innerText = fullText.substring(0, 30) + '...';
            } else {
                span.innerText = fullText;
            }
        });
    }

    // Initialize on page load for any existing content
    initializeTruncatedText();
</script>
<link href="https://cdn.datatables.net/1.10.21/css/jquery.dataTables.min.css" rel="stylesheet" />
<script src="//cdn.datatables.net/1.10.21/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.print.min.js"></script>

<script>
   
   $(document).ready(function() {
      if($("#myTableR").length > 0) {
         // Get search parameters from URL
         const urlParams = new URLSearchParams(window.location.search);
         const vendor = urlParams.get('vendor') || '';
         const filter = urlParams.get('filter') || '';
         const search = urlParams.get('search') || '';

         // Only initialize DataTable if search was performed
         if(search === 'search') {
            $("#myTableR").show(); // Show the table
            $("#myTableR").DataTable({
               "processing": true,
               "serverSide": true,
               "deferRender": true,
               "ajax": {
                  "url": "report_ajax.php",
                  "type": "POST",
                  "data": function(d) {
                     d.vendor = vendor;
                     d.filter = filter;
                  },
                  "error": function(xhr, error, thrown) {
                     console.error('DataTables AJAX error:', error, thrown);
                     alert('Error loading data. Please try again.');
                  }
               },
               "columnDefs": [
                  { "targets": "sort-date", "type": "date" },
                  {targets: 'no-sort', orderable: false},
                  {targets: 2, orderable: false} // Code column
               ],
               "aaSorting": [],
               "pageLength": 30,
               "lengthMenu": [
                  [10, 25, 30, 50, 100],
                  [10, 25, 30, 50, 100]
               ],
               "dom": 'Bfrtip',
               "buttons": [
                  'excel', 'pdf', 'print'
               ],
               "language": {
                  "processing": "Loading data...",
                  "emptyTable": "No data available",
                  "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                  "infoEmpty": "Showing 0 to 0 of 0 entries",
                  "infoFiltered": "(filtered from _MAX_ total entries)"
               },
               "drawCallback": function(settings) {
                  $("#myTableR td").css("white-space", "initial");
                  // Re-initialize the toggle functionality for truncated text
                  initializeTruncatedText();
               }
            });
         }
      }
   });
</script>