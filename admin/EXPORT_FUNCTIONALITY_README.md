# CSV Export Functionality

## Overview
A comprehensive CSV export system has been added to export vendor rates data in various formats and with flexible filtering options.

## Features Added

### 1. Main Export Page (`export_rates.php`)
- **Location**: Admin menu → "Export Rates"
- **Features**:
  - Export all vendor rates or filtered subsets
  - Filter by vendor, destination, date range
  - Real-time statistics display
  - User-friendly interface with progress indicators

### 2. Quick Export from Report Page
- **Location**: Report page → "Export Current Search Results" button
- **Features**:
  - Export currently filtered/searched results
  - Maintains the same filters as the report view
  - Quick one-click export

### 3. CSV Generation Engine (`export_csv.php`)
- **Features**:
  - Optimized for large datasets (up to 500MB memory, 5-minute timeout)
  - UTF-8 BOM encoding for Excel compatibility
  - Intelligent filename generation with timestamps
  - Progress indicators for large exports
  - Comprehensive error handling

## File Structure

```
admin/
├── export_rates.php              # Main export interface
├── export_csv.php                # CSV generation engine
├── export_current_results.php    # Quick export for search results
├── export_log_table.sql          # Optional logging table
└── EXPORT_FUNCTIONALITY_README.md # This documentation
```

## CSV File Format

### Columns Included:
1. **ID** - Unique record identifier
2. **Vendor Name** - Full vendor name (resolved from vendor ID)
3. **Destination** - Destination country/region
4. **Code** - Rate code/prefix
5. **Rate (USD)** - Formatted price with currency symbol
6. **Effective Date** - Human-readable date format
7. **Export Date** - When the export was generated

### File Features:
- UTF-8 encoding with BOM for Excel compatibility
- Comma-separated values with proper escaping
- Summary section with export statistics
- Filter information appended at the end
- Intelligent filename generation

## Usage Instructions

### Method 1: Full Export Interface
1. Navigate to **Admin → Export Rates**
2. Choose optional filters:
   - **Vendor**: Select specific vendor or leave blank for all
   - **Destination**: Enter partial destination name
   - **Date Range**: Set from/to dates
3. Click **"Export to CSV"**
4. File will download automatically

### Method 2: Quick Export from Report
1. Go to **Admin → Report**
2. Use search filters to find desired data
3. Click **"Export Current Search Results"**
4. Filtered results will download as CSV

## Performance Optimizations

### Database Optimizations:
- Uses JOIN queries to avoid N+1 query problems
- Leverages database indexes for fast filtering
- Streams data to avoid memory issues with large datasets

### Memory Management:
- 512MB memory limit for large exports
- 5-minute execution timeout
- Output buffering every 1000 rows
- Efficient data streaming

### File Generation:
- Progressive CSV writing
- UTF-8 BOM for Excel compatibility
- Intelligent filename generation
- Comprehensive error handling

## Installation Steps

### 1. Files are already created and ready to use

### 2. Optional: Create Export Log Table
```sql
-- Run this SQL to enable export logging
CREATE TABLE IF NOT EXISTS `export_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `export_type` varchar(50) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `record_count` int(11) NOT NULL DEFAULT 0,
  `filters` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 3. Navigation Menu Updated
- "Export Rates" menu item added to admin sidebar
- Export button added to report page

## Filename Convention

Exported files follow this naming pattern:
```
vendor_rates_[vendor_name]_[destination_filter]_[date_range]_YYYY-MM-DD_HH-MM-SS.csv
```

Examples:
- `vendor_rates_2024-01-15_14-30-25.csv` (all data)
- `vendor_rates_Acme_Corp_2024-01-15_14-30-25.csv` (specific vendor)
- `vendor_rates_United_States_2024-01-15_14-30-25.csv` (destination filter)
- `vendor_rates_Acme_Corp_United_2024-01-01_to_2024-01-31_2024-01-15_14-30-25.csv` (full filters)

## Error Handling

### Common Issues and Solutions:

1. **"Database error" message**
   - Check database connection
   - Verify table permissions
   - Check MySQL error logs

2. **"Memory limit exceeded"**
   - Increase PHP memory_limit in php.ini
   - Use more specific filters to reduce dataset size
   - Contact system administrator

3. **"Maximum execution time exceeded"**
   - Increase max_execution_time in php.ini
   - Use date range filters to limit data
   - Export in smaller batches

4. **Excel not opening CSV correctly**
   - File includes UTF-8 BOM for Excel compatibility
   - Try opening with "Data → From Text/CSV" in Excel
   - Use LibreOffice Calc as alternative

## Security Features

- All user inputs are sanitized with `mysqli_real_escape_string()`
- Session validation required for access
- File downloads use proper headers to prevent XSS
- No direct file system access - all data streamed

## Monitoring and Analytics

### Export Statistics:
- Track export frequency and usage patterns
- Monitor file sizes and generation times
- User activity logging (if export_log table is created)

### Performance Monitoring:
```sql
-- View recent export activity
SELECT 
    filename,
    record_count,
    created_at
FROM export_log 
ORDER BY created_at DESC 
LIMIT 10;
```

## Customization Options

### Modify CSV Columns:
Edit the `$headers` array in `export_csv.php`:
```php
$headers = array(
    'ID',
    'Vendor Name',
    'Destination',
    // Add or remove columns as needed
);
```

### Change File Format:
- Modify delimiter: Change `fputcsv()` parameters
- Add different encoding: Modify BOM output
- Change date format: Modify `$general->dateToRead()` calls

### Adjust Performance Settings:
```php
// In export_csv.php
ini_set('memory_limit', '1024M');    // Increase memory
set_time_limit(600);                 // 10 minutes timeout
```

## Support and Troubleshooting

### Debug Mode:
Add this to the top of `export_csv.php` for debugging:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

### Log Analysis:
Check PHP error logs and MySQL slow query logs for performance issues.

### Performance Testing:
Use the existing `performance_test.php` to benchmark export performance.

## Version History
- v1.0 - Initial CSV export functionality
- v1.1 - Added filtering and quick export options
- v1.2 - Performance optimizations and error handling
- v1.3 - Excel compatibility and comprehensive documentation
