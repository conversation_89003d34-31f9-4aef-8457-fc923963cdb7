<?php
$page = "Destination";
$link = "rateGenerate.php";
$table = "rateGenerate";
$back = "dashboard.php";
if (isset($_GET["action"]))
    $back = "rateGenerate.php";
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_SESSION["msg"]) && $_SESSION["msg"] != "") {
    $message = $_SESSION["msg"];
    unset($_SESSION["msg"]);
}
$rateID = false;
// print_r($_REQUEST);
if (isset($_REQUEST["rate_id"]) && $_REQUEST["rate_id"] != "") {
    $rateID = mysqli_real_escape_string($connection, $_REQUEST["rate_id"]);
}
if($rateID == false)
{
    $message = '<div class="alert alert-danger" role="alert">Invalid Rate Generator1</div>';
    $_SESSION["msg"] = $message;
    echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
    exit;
}
else
{
    // select from database
    $strSQL = mysqli_query($connection, "select * from `$table` where id='" . $rateID . "'");
    if (mysqli_num_rows($strSQL) > 0) {
        $results    = mysqli_fetch_array($strSQL);
        $name = $results['name'];
        $trunk = $results['trunk'];
        $currency = $results['currency'];
        $account = $results['account_id'];
        $prefix = $results['prefix'];
    } else {
        $message = '<div class="alert alert-danger" role="alert">Invalid Rate Generator</div>';
        $_SESSION["msg"] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    }
}
if (isset($_GET["action"])) {
    if ($_GET["action"] == "add") {
        $form = "add";
    } elseif ($_GET["action"] == "edit") {
        $form = "edit";
        $id       = mysqli_real_escape_string($connection, $_GET["id"]);
        $strSQL = mysqli_query($connection, "select * from `rateRules` where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $results    = mysqli_fetch_array($strSQL);
            
            $destinations = $results['destinations'];
            $areas = explode(",",$results['areas']);

            
            $rate = $results['rate'];
            $billing_cycle = $results['billing_cycle'];
            $remarks = $results['remarks'];
            $effective = $results['effective'];

            
        } else {
            $message = '<div class="alert alert-danger" role="alert">Invalid ' . $page . '</div>';
            $_SESSION["msg"] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        }
    } elseif ($_GET['action'] == "delete") {
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select id from `rateRules` where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $strSQL = mysqli_query($connection, "delete from `rateRules` where id='" . $id . "'");

            $message = '<div class="alert alert-danger" role="alert">' . $page . ' deleted</div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=rateRules.php?action=add&rate_id=' . $rate_id . '" />';
            exit;
        } else {
            $message = '<div class="alert alert-danger" role="alert">Invalid ' . $page . '</div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=rateRules.php?action=add&rate_id=' . $rate_id . '" />';
            exit;
        }
    }
}
if (isset($_POST['action'])) {
    if ($_POST['action'] == "add") {
        $destinations = mysqli_real_escape_string($connection, $_POST['destinations']);
        $rate = mysqli_real_escape_string($connection, $_POST['rate']);
        // array to string
        $areas = $_POST['areas'];
        if(is_array($areas))
        {
            $areas = implode(",", $areas);
        }
        $billing_cycle = mysqli_real_escape_string($connection, $_POST['billing_cycle']);
        $remarks = mysqli_real_escape_string($connection, $_POST['remarks']);
        $effective = mysqli_real_escape_string($connection, $_POST['effective']);
        // remarks no change dates have to be same
        if($remarks == 'No Change')
        {
            $effective = date('Y-m-d');
        }
        $rate_id = mysqli_real_escape_string($connection, $_POST['rate_id']);

        $sql = "INSERT INTO `rateRules` (`rate_id`, `destinations`, `areas`, `rate`, `billing_cycle`, `remarks`, `effective`) VALUES('$rate_id', '$destinations', '$areas', '$rate', '$billing_cycle', '$remarks', '$effective')";



        mysqli_query($connection, $sql) or die(mysqli_error($connection));
        $id = mysqli_insert_id($connection);




        $message = '<div class="alert alert-success" role="alert">' . $page . ' Added</div>';
        $id = mysqli_insert_id($connection);
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=rateRules.php?rate_id=' . $rate_id . '" />';
        exit;
    } elseif ($_POST['action'] == "edit") {
        // print_r($_POST);
        $destinations = mysqli_real_escape_string($connection, $_POST['destinations']);
        $areas = $_POST['areas'];
        if(is_array($areas))
        {
            $areas = implode(",", $areas);
        }
        $rate = mysqli_real_escape_string($connection, $_POST['rate']);
        $billing_cycle = mysqli_real_escape_string($connection, $_POST['billing_cycle']);
        $remarks = mysqli_real_escape_string($connection, $_POST['remarks']);
        $effective = mysqli_real_escape_string($connection, $_POST['effective']);
        // remarks no change dates have to be same
        if($remarks == 'No Change')
        {
            $effective = date('Y-m-d');
        }
        

        $id     = mysqli_real_escape_string($connection, $_POST['id']);

        $sql = "UPDATE `rateRules` SET `destinations` = '$destinations', `areas` = '$areas', `rate` = '$rate', `billing_cycle` = '$billing_cycle', `remarks` = '$remarks', `effective` = '$effective' WHERE id='$id'";
        // echo $sql;
        // exit;

        $result = mysqli_query($connection, $sql);

        $message = '<div class="alert alert-success" role="alert">' . $page . ' updated</div>';
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=rateRules.php?rate_id=' . $rateID . '" />';
        exit;
    } else {
        $message = '<div class="alert alert-danger" role="alert">Error occured please try again</div>';
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    }
}

?>
<style>
    .form-control {
        width: 100%;
    }

    .input-group-text {
        padding: 4px 14px 4px 0px;
        width: 50%;
        height: 34px;
    }

    .input-group-prepend {
        margin-right: -10px;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }

    .bootstrap-select.form-control-lg .dropdown-toggle {
        padding: 0.375rem 1rem;
    }
</style>
<style>
         .bootstrap-select.form-control-lg .dropdown-toggle {
            padding: 0.5rem 1rem;
            border: 1px solid #cccccc;
            background-color: white;
         }
      </style>
<!-- page content -->
<div class="content-wrapper">
    <section class="content-header">
        <div class="header-icon">
            <i class="fa fa-edit"></i>
        </div>
        <div class="header-title">
            <h1>Rules for: <?php echo $name ?></h1>
            <small>&nbsp;</small>
        </div>
    </section>
    <section class="content">
        <?php echo isset($message) ? $message : ''; ?> <div class="row">
            <div class="col-lg-12 grid-margin stretch-card">
                <div class="card card2">
                    <div class="card-body">
                        <?php if (isset($form)) { ?> <div class="cont1ainer">
                                <form id="dataForm" action="rateRules.php" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                                    <?php if ($form == "edit") { ?>
                                        <input type="hidden" name="id" value="<?php echo $id; ?>">
                                    <?php } ?>
                                    <input type="hidden" name="rate_id" value="<?php echo $rateID; ?>">
                                    <div class="row">
                                        <div class="form-group col-lg-6">
                                            <label for="destinations">Destination:</label>
                                            <select name="destinations" id="destinations" class="form-control form-control-lg " required>
                                                <option value="">Select  *</option>
                                                <?php
                                                $query = "SELECT id, `name` FROM `destinations` where parent=0 order by `name`";
                                                $result = mysqli_query($connection, $query);

                                                while ($rows = mysqli_fetch_array($result)) {
                                                    if (isset($form) && ($form == "edit" || $form == "add") && $destinations == $rows['id']) {
                                                ?>
                                                        <option selected="selected" value="<?php echo $rows['id']; ?>"><?php echo $rows['name']; ?></option>
                                                    <?php
                                                    } else {
                                                    ?>
                                                        <option value="<?php echo $rows['id']; ?>"><?php echo $rows['name']; ?></option>
                                                <?php
                                                    }
                                                }
                                                ?>
                                            </select>
                                            <div class="invalid-feedback">Please select a customer</div>
                                        </div>
                                        <div class="form-group col-lg-6">
                                            <label for="areas">Areas:</label>
                                            <select name="areas[]" id="areas" multiple class="form-control form-control-lg selectpicker22" required>
                                                <option value="">Select  *</option>
                                                
                                            </select>
                                            <div class="invalid-feedback">Please select a customer</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="name">Rate:</label>
                                            <input maxlength="10" oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);" pattern="[0-9]+([\.,][0-9]+)?" step="0.000001" max="100" type="number" name="rate" value="<?php echo $rate; ?>" class="form-control inRetainage" placeholder="Rate" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-lg-6">
                                            <label for="name">Billing Cycle</label>
                                            <select name="billing_cycle" id="billing_cycle" class="form-control form-control-lg " required>
                                                <option value="">Select  *</option>
                                                <option value="1/1" <?php echo (isset($billing_cycle) && $billing_cycle == '1/1') ? 'selected' : ''; ?>>1/1</option>
                                                <option value="60/1" <?php echo (isset($billing_cycle) && $billing_cycle == '60/1') ? 'selected' : ''; ?>>60/1</option>
                                                <option value="60/60" <?php echo (isset($billing_cycle) && $billing_cycle == '60/60') ? 'selected' : ''; ?>>60/60</option>
                                                <option value="6/6" <?php echo (isset($billing_cycle) && $billing_cycle == '6/6') ? 'selected' : ''; ?>>6/6</option>
                                                
                                            </select>
                                            <div class="invalid-feedback">Please select a account</div>
                                        </div>


                                        <div class="form-group col-lg-6">
                                            <label for="name">Remarks</label>
                                            <select name="remarks" id="remarks" class="form-control form-control-lg " required>
                                                <option value="">Select  *</option>
                                                <option value="New" <?php echo (isset($remarks) && $remarks == 'New') ? 'selected' : ''; ?>>New</option>
                                                <option value="Increase" <?php echo (isset($remarks) && $remarks == 'Increase') ? 'selected' : ''; ?>>Increase</option>
                                                <option value="Decrease" <?php echo (isset($remarks) && $remarks == 'Decrease') ? 'selected' : ''; ?>>Decrease</option>
                                                <option value="No Change" <?php echo (isset($remarks) && $remarks == 'No Change') ? 'selected' : ''; ?>>No Change</option>
                                            </select>
                                            <div class="invalid-feedback">Please select a prefix</div>
                                        </div>
                                        <div class="form-group col-md-6 effectiveDiv">
                                            <label for="trunk">Effective Date:</label>
                                            <input type="date" class="form-control" id="effective" name="effective" value="<?php echo isset($effective) ? $effective : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        
                                        
                                        
                                        <div class="mt-3 col-lg-12">
                                            <input type="hidden" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">
                                            <button type="submit" id="btnSubmit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        <?php } else { ?> <?php $query = "SELECT * FROM `rateRules` where rate_id=$rateID"; ?> <div class="table-responsive">
                                <table class="table table2" id="myTable">
                                    <thead>
                                        <tr>
                                            <th>Destination</th>
                                            <th>Code</th>
                                            <th>Rate USD</th>
                                            <th>Billing Cycle</th>
                                            <th>Prefix</th>
                                            <th>Remarks</th>
                                            <th>Effective Date</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $result = mysqli_query($connection, $query);
                                        while ($rows = mysqli_fetch_array($result)) {

                                            $action = " <a data-toggle='tooltip' data-placement='top' title='Edit' href='?action=edit&rate_id=" . $rows['rate_id'] . "&id=" . $rows['id'] . "' class='btn-info btn btn-sm'><i class='fa fa-pencil'></i></a> 
                                                        <a data-toggle='tooltip' data-placement='top' title='Delete' href='?action=delete&rate_id=" . $rows['rate_id'] . "&id=" . $rows['id'] . "' onclick=\"return confirm('Are you sure you want to DELETE this Record?');\" class='btn-danger btn btn-sm'><i class='fa fa-trash-o'></i></a>";
                                            // if areas is -1 then destination all destinations
                                            $areas = explode(",", $rows['areas']);
                                            foreach($areas as $area)
                                            {
                                            
                                            
                                            if($area == -1)
                                            {
                                                $destinations = "All ".$general->getDestinationNameByID($rows['destinations']);
                                                $codes = $general->getDestinationCodes($rows['destinations']);
                                            }
                                            else
                                            {
                                                $destinations = $general->getDestinationNameByID($area);
                                                $codes = $general->getDestinationCodes($area);
                                            }
                                            // echo $rows['areas'];
                                            echo "
                                                <tr>
                                                    <td>" . $destinations . "</td>
                                                    <td>" . $codes ."</td>
                                                    <td>" . $rows['rate'] . "</td>
                                                    <td>" . $rows['billing_cycle'] . "</td>
                                                    <td>" . $general->getPrefixNameByID($rows['rate_id']) . "</td>
                                                    <td>" . $rows['remarks'] . "</td>
                                                    <td>" . $rows['effective'] . "</td>
                                                    <td>$action</td>
                                                </tr>";
                                            }
                                        }
                                        ?></tbody>
                                </table>
                            </div> <?php } ?> </div>
                </div>
            </div>
        </div>

    </section>
</div>
<!-- /page content -->
<?php include("includes/footer.php"); ?>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/css/bootstrap-select.min.css">
<script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/js/bootstrap-select.min.js"></script>
<script>
    $(function() {
        $('.selectpicker22').selectpicker({liveSearch:true});
        $('[data-toggle="tooltip"]').tooltip()
        
        $("#btnSubmit").on("click", function(e) {
            var form = $("#dataForm")[0];
            var isValid = form.checkValidity();

            if (!isValid) {
                e.preventDefault();
                e.stopPropagation();
            } else {
                form.submit();
            }
            form.classList.add('was-validated');
            return false; // For testing only to stay on this page
        });
        // on account dropdown change get prefix list
        $('#remarks').on('change', function() {
            $('.effectiveDiv').show();
            $('#effective').attr('disabled', false);
            // if remarks new update effective date to current date
            if ($(this).val() == 'New') {
                $('#effective').val(new Date().toISOString().slice(0, 10));
            }
            // if remarks increase update effective date to 7 days from current date
            if ($(this).val() == 'Increase') {
                var date = new Date();
                date.setDate(date.getDate() + 7);
                $('#effective').val(date.toISOString().slice(0, 10));
            }
            // if remarks decrease update effective date to current date
            if ($(this).val() == 'Decrease') {
                $('#effective').val(new Date().toISOString().slice(0, 10));
            }
            // if remarks no change update effective date hide .effectiveDiv
            if ($(this).val() == 'No Change') {
                // disable effective date
                $('#effective').attr('disabled', true);
                // hide effective date
                $('.effectiveDiv').hide();
            }
        });
        $('#destinations').on('change', function() {
            var id = $(this).val();
            $.ajax({
                url: 'ajaxLoad.php',
                type: 'GET',
                data: {
                    action: 'getchildDestinations',
                    id: id
                },
                success: function(data) {
                    var areas = $('#areas');
                    areas.empty();
                    areas.append('<option value="-1">All Areas</option>');
                    // console.log(data);
                    $.each(data.areas, function(index, value) {
                        // console.log(index);
                        // console.log(value);
                        areas.append('<option value="' + index + '">' + value + '</option>');
                    });
                    $('.selectpicker22').selectpicker('destroy');
                    $('.selectpicker22').selectpicker();
                }
            });
        });
        <?php if (isset($form) && $form == "edit") { ?>
            
        var id = <?php echo $destinations; ?>;
            $.ajax({
                url: 'ajaxLoad.php',
                type: 'GET',
                data: {
                    action: 'getchildDestinations',
                    id: id
                },
                success: function(data) {
                    var areas = $('#areas');
                    areas.empty();
                    areas.append('<option value="-1">All Areas</option>');
                    // console.log(data);
                    $.each(data.areas, function(index, value) {
                        // console.log(index);
                        // console.log(value);
                        areas.append('<option value="' + index + '">' + value + '</option>');
                    });
                    $('.selectpicker22').selectpicker('refresh');
                    // $('.selectpicker22').selectpicker();
                    // select picker multiple select coma seperated
                    $('.selectpicker22').selectpicker('val', <?php echo json_encode($areas); ?>);



                    

                    
                    // $('.selectpicker22').selectpicker({liveSearch:true});
                }
            });
        <?php } ?>
    });
</script>