<?php
$page = "Account";
$link = "account.php";
$table = "account";
$back = "dashboard.php";
if (isset($_GET["action"]))
    $back = "account.php";
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_SESSION["msg"]) && $_SESSION["msg"] != "") {
    $message = $_SESSION["msg"];
    unset($_SESSION["msg"]);
}
if (isset($_GET["action"])) {
    if ($_GET["action"] == "add") {
        $form = "add";
    } elseif ($_GET["action"] == "edit") {
        $form = "edit";
        $id       = mysqli_real_escape_string($connection, $_GET["id"]);
        $strSQL = mysqli_query($connection, "select * from `$table` where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $results    = mysqli_fetch_array($strSQL);
            $fname = $results['fname'];
            $lname = $results['lname'];
            $accountNumber = $results['accountNumber'];
            $accountName = $results['accountName'];
            $website = $results['website'];
            $email = $results['email'];
            $phone = $results['phone'];
            $billingEmail = $results['billingEmail'];
            $ratesEmail = $results['ratesEmail'];
            $technicalEmail = $results['technicalEmail'];
            $type = $results['type'];
            $prefix = $results['prefix'];
        } else {
            $message = '<div class="alert alert-danger" role="alert">Invalid ' . $page . '</div>';
            $_SESSION["msg"] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        }
    } elseif ($_GET['action'] == "delete") {
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select id from `$table` where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $strSQL = mysqli_query($connection, "delete from `$table` where id='" . $id . "'");

            $message = '<div class="alert alert-danger" role="alert">' . $page . ' deleted</div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        } else {
            $message = '<div class="alert alert-danger" role="alert">Invalid ' . $page . '</div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        }
    }
}
if (isset($_POST['action'])) {
    if ($_POST['action'] == "edit") {
        $fname = mysqli_real_escape_string($connection, $_POST['fname']);
        $lname = ' ';
        $accountNumber = "1";
        $accountName = mysqli_real_escape_string($connection, $_POST['accountName']);
        $website = mysqli_real_escape_string($connection, $_POST['website']);
        $email = mysqli_real_escape_string($connection, $_POST['email']);
        $phone = mysqli_real_escape_string($connection, $_POST['phone']);
        $billingEmail = mysqli_real_escape_string($connection, $_POST['billingEmail']);
        $ratesEmail = mysqli_real_escape_string($connection, $_POST['ratesEmail']);
        $technicalEmail = mysqli_real_escape_string($connection, $_POST['technicalEmail']);
        $type = mysqli_real_escape_string($connection, $_POST['type']);
        $prefix = mysqli_real_escape_string($connection, $_POST['prefix']);


        $id     = mysqli_real_escape_string($connection, $_POST['id']);
        $uid = userid;
        


        $sql = "UPDATE `$table` SET 
                `fname` = '$fname', 
                `lname` = '$lname', 
                `accountNumber` = '$accountNumber', 
                `accountName` = '$accountName', 
                `website` = '$website', 
                `email` = '$email', 
                `phone` = '$phone', 
                `billingEmail` = '$billingEmail', 
                `ratesEmail` = '$ratesEmail', 
                `technicalEmail` = '$technicalEmail', 
                `prefix` = '$prefix', 
                `type` = '$type' $picture WHERE id='$id'";

        $result = mysqli_query($connection, $sql);

        $message = '<div class="alert alert-success" role="alert">' . $page . ' updated</div>';
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    } else {
        $message = '<div class="alert alert-danger" role="alert">Error occured please try again</div>';
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    }
}

?>
<style>
    .form-control {
        width: 100%;
    }

    .input-group-text {
        padding: 4px 14px 4px 0px;
        width: 50%;
        height: 34px;
    }

    .input-group-prepend {
        margin-right: -10px;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }

    .bootstrap-select.form-control-lg .dropdown-toggle {
        padding: 0.375rem 1rem;
    }
    .bootstrap-tagsinput{
        width: 100%;
    }
</style>
<!-- page content -->
<div class="content-wrapper">
    <section class="content-header">
        <div class="header-icon">
            <i class="fa fa-edit"></i>
        </div>
        <div class="header-title">
            <h1><?php echo $page ?> Management</h1>
            <small>Add, Update or Delete <?php echo $page ?></small>
        </div>
    </section>
    <section class="content">




        
        <?php echo isset($message) ? $message : ''; ?> <div class="row">
            <div class="col-lg-12 grid-margin stretch-card">
                <div class="card card2">
                    <div class="card-body">
                        <?php if (isset($form)) { ?> <div class="cont1ainer">
                                <form id="dataForm" action="<?php echo $link; ?>" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                                    <?php if ($form == "edit") { ?>
                                        <input type="hidden" name="id" value="<?php echo $id; ?>">
                                    <?php } ?>
                                    <div class="row">
                                        <div class="form-group col-md-6">
                                            <label for="fname">Company:</label>
                                            <input type="text" class="form-control" id="fname" name="fname" value="<?php echo isset($fname) ? $fname : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="accountName">Account Name:</label>
                                            <input type="text" class="form-control" id="accountName" name="accountName" value="<?php echo isset($accountName) ? $accountName : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="website">Website:</label>
                                            <input type="text" class="form-control" id="website" name="website" value="<?php echo isset($website) ? $website : ''; ?>">
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="email">Email:</label>
                                            <input type="email" class="form-control" id="email" name="email" value="<?php echo isset($email) ? $email : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="phone">Phone:</label>
                                            <input type="phone" class="form-control" id="phone" name="phone" value="<?php echo isset($phone) ? $phone : ''; ?>">
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="billingEmail">Billing Email:</label>
                                            <input type="email" class="form-control" id="billingEmail" name="billingEmail" value="<?php echo isset($billingEmail) ? $billingEmail : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="ratesEmail">Rates Email:</label>
                                            <input type="email" class="form-control" id="ratesEmail" name="ratesEmail" value="<?php echo isset($ratesEmail) ? $ratesEmail : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="technicalEmail">Technical Email:</label>
                                            <input type="email" class="form-control" id="technicalEmail" name="technicalEmail" value="<?php echo isset($technicalEmail) ? $technicalEmail : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="type">Prefix:</label>
                                            <input type="text" class="form-control input-tags" data-role="tagsinput" id="prefix" name="prefix" value="<?php echo isset($prefix) ? $prefix : ''; ?>">
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="type">Type:</label>
                                            <input type="text" class="form-control" id="type" name="type" value="<?php echo isset($type) ? $type : ''; ?>">
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="mt-3 col-lg-12">
                                            <input type="hidden" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">
                                            <button type="submit" id="btnSubmit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        <?php } else { ?> 
                        <?php 
                        $query = "SELECT * FROM `$table`";
                        ?> 
                            <div class="table-responsive">
                                <table class="table table2" id="myTable">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Account Number</th>
                                            <th>Account Name</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>Prefix</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $result = mysqli_query($connection, $query);
                                        while ($rows = mysqli_fetch_array($result)) {

                                            $action = " <a href='?action=edit&id=" . $rows['id'] . "' class='btn-info btn btn-sm'><i class='fa fa-pencil'></i></a> 
                                                        <a href='?action=delete&id=" . $rows['id'] . "' onclick=\"return confirm('Are you sure you want to DELETE this Record?');\" class='btn-danger btn btn-sm'><i class='fa fa-trash-o'></i></a>";

                                            echo "
                                        <tr>
                                            <td>" . $rows['fname'] . " " . $rows['lname'] . "</td>
                                            <td>" . $rows['accountNumber'] . "</td>
                                            <td>" . $rows['accountName'] . "</td>
                                            <td>" . $rows['email'] . "</td>
                                            <td>" . $rows['phone'] . "</td>
                                            <td>" . $rows['type'] . "</td>
                                            <td>$action</td>
                                        </tr>";
                                        }
                                        ?>
                                        </tbody>
                                </table>
                            </div> 
                            <?php } ?> 
                        </div>
                </div>
            </div>
        </div>

    </section>
</div>
<!-- /page content -->
<?php include("includes/footer.php"); ?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-tagsinput/0.8.0/bootstrap-tagsinput.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-tagsinput/0.8.0/bootstrap-tagsinput.js"></script>
<script>
    $(function() {
        $("#btnSubmit").on("click", function(e) {
            var form = $("#dataForm")[0];
            var isValid = form.checkValidity();

            if (!isValid) {
                e.preventDefault();
                e.stopPropagation();
            } else {
                form.submit();
            }
            form.classList.add('was-validated');
            return false; // For testing only to stay on this page
        });
    });
</script>