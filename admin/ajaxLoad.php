<?php
// header('Access-Control-Allow-Origin: *');
// header('Content-Type: application/json');
include_once('../includes/config.php');
include_once('includes/session.php');
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

// if (isset($_GET['action'])) {
// }

if (isset($_POST['action'])) {
    if ($_POST['action'] == "sortMe") {
        // print_r($_POST);
        foreach($_POST['parentIds'] as $key => $value)
        {
            $strSQL = mysqli_query($connection, "UPDATE `activities_cat` set sortMe='$key' where id='" . $value . "'");   
            if(!empty($_POST['childIds'][$key]))
            {
                foreach($_POST['childIds'][$key] as $key1 => $value1)
                {
                    // echo "UPDATE `activities_cat` set parent=$value, sortMe='$key1' where id='" . $value1 . "'\n";
                    $strSQL = mysqli_query($connection, "UPDATE `activities_cat` set parent=$value, sortMe='$key1' where id='" . $value1 . "'");   
                    // echo "=>".$value1;
                }
            }
        }
        $__data['message'] = 'updated';
        echo json_encode($__data);
        exit;
    }
    if ($_POST['action'] == "sortMeList") {
        // print_r($_POST);
        foreach($_POST['parentChildIds'] as $key => $value)
        {
            if(!empty($_POST['childIds'][$key]))
            {
                foreach($_POST['childIds'][$key] as $key1 => $value1)
                {
                    $strSQL = mysqli_query($connection, "UPDATE `activities_list` set activity_id=".$value[0].", sortMe='$key1' where id='" . $value1 . "'");   
                    // echo "UPDATE `activities_list` set activity_id=".$value[0].", sortMe='$key1' where id='" . $value1 . "'\n";
                }
            }
            
        }
        $__data['message'] = 'updated';
        echo json_encode($__data);
        exit;
    }
    if ($_POST['action'] == "addQoutationStep") {
        $name = mysqli_real_escape_string($connection, $_POST['name']);
        $address = mysqli_real_escape_string($connection, $_POST['address']);
        $zip = mysqli_real_escape_string($connection, $_POST['zip']);
        $contact = mysqli_real_escape_string($connection, $_POST['contact']);
        $email = mysqli_real_escape_string($connection, $_POST['email']);
        $company = mysqli_real_escape_string($connection, $_POST['company']);
        $service1 = 0;
        $service2 = 0;
        $disscount = 0;
        $id = false;
        if (isset($_POST['service1']) && $_POST['service1'] != "")
            $service1 = mysqli_real_escape_string($connection, $_POST['service1']);
        if (isset($_POST['service2']) && $_POST['service2'] != "")
            $service2 = mysqli_real_escape_string($connection, $_POST['service2']);
        if (isset($_POST['disscount']) && $_POST['disscount'] != "")
            $disscount = mysqli_real_escape_string($connection, $_POST['disscount']);

        if (isset($_POST['id']))
            $id = mysqli_real_escape_string($connection, $_POST['id']);

        $data = json_encode($_POST['items']);

        $date = date("Y-m-d", time());

        if ($id==false) {
            $query = "INSERT INTO `quotation`(`userid`, `updatedBy`, `date`, `name`, `address`, `zip`, `contact`, `email`, `data`, `company`, `service1`, `service2`, `disscount`) 
            VALUES ('" . userid . "','" . userid . "', '$date', '$name', '$address', '$zip', '$contact', '$email', '$data', '$company', '$service1', '$service2', '$disscount')";
            // echo $query;
            mysqli_query($connection, $query);
            $id = mysqli_insert_id($connection);
            $__data['result'] = "inserted";
            // $__data['result'] = $query;
            $__data['id'] = $id;
            echo json_encode($__data);
        } else {
            $query = "UPDATE `quotation` SET 
            `name`='$name',`address`='$address',`zip`='$zip',`contact`='$contact',`email`='$email',`data`='$data',`company`='$company',`service1`='$service1',`service2`='$service2',`disscount`='$disscount',  updatedBy='" . userid . "'
            WHERE `id`='$id'";
            mysqli_query($connection, $query);
            $__data['result'] = "updated";
            $__data['id'] = $id;
            echo json_encode($__data);
        }
        exit;
    }
}