<?php
// Database connection info
$dbdetails = include('includes/config.php');


// DB table to use
$table = 'account.php';

// Table's primary key

$primaryKey = 'id';

// Array of database columns which should be read and sent back to DataTables.
// The db parameter represents the column name in the database.
// The 'dt parameter represents the DataTables column identifier.

$columns = array(
	
   array('db' => 'fname', 'dt' => 0),
   array('db' => 'accountNumber', 'dt' => 1), 
   array('db' => 'accountName', 'dt' => 2), 
   array('db' => 'email', 'dt' => 3), 
   array('db' => 'phone', 'dt' => 4)
);

//Include sql query processing class
require 'ssp.class.php';

//output data as json format
echo json_encode(
	SSP::simple($_GET, $dbdetails, $table, $primaryKey, $columns)
);