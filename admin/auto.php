<?php
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');
include_once('../includes/config.php');
include_once('../includes/session.php');
if(isset($_GET['term']))
{
    $q = mysqli_real_escape_string($connection, $_GET['term']);
    $result = mysqli_query($connection, "SELECT `name` FROM `destinations` where `name` like '%$q%' and parent=0 limit 50");
    $ret = array();
    while ($rows = mysqli_fetch_assoc($result)) {
        $ret[] = trim($rows['name']);
    }
    echo json_encode($ret);
}
exit;

// if(isset($_GET['term']))
// {
//     $column = mysqli_real_escape_string($connection, $_GET['column']);
//     $q      = mysqli_real_escape_string($connection, $_GET['term']);
//     $result = mysqli_query($connection, "SELECT `$column` as `term` FROM `data` where `$column` like '%$q%' group by `$column` limit 50");
//     $ret    = array();
//     while ($rows = mysqli_fetch_assoc($result)) {
//         $ret[] = $rows['term'];
//     }
//     echo json_encode($ret);
// }