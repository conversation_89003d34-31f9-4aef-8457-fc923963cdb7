<?php
$page = "Currency";
$link = "currency.php";
$table = "currency";
$back = "dashboard.php";
if (isset($_GET["action"]))
    $back = "currency.php";
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);

if (isset($_SESSION["msg"]) && $_SESSION["msg"] != "") {
    $message = $_SESSION["msg"];
    unset($_SESSION["msg"]);
}
if (isset($_GET["action"])) {
    if ($_GET["action"] == "add") {
        $form = "add";
    } elseif ($_GET["action"] == "edit") {
        $form = "edit";
        $id       = mysqli_real_escape_string($connection, $_GET["id"]);
        $strSQL = mysqli_query($connection, "select * from `$table` where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $results    = mysqli_fetch_array($strSQL);
            $name = $results['name'];
            $code = $results['code'];
            $symbol = $results['symbol'];
        } else {
            $message = '<div class="alert alert-danger" role="alert">Invalid ' . $page . '</div>';
            $_SESSION["msg"] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        }
    } elseif ($_GET['action'] == "delete") {
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select id from `$table` where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $strSQL = mysqli_query($connection, "delete from `$table` where id='" . $id . "'");

            $message = '<div class="alert alert-danger" role="alert">' . $page . ' deleted</div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        } else {
            $message = '<div class="alert alert-danger" role="alert">Invalid ' . $page . '</div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        }
    }
}
if (isset($_POST['action'])) {
    if ($_POST['action'] == "add") {
        $name = mysqli_real_escape_string($connection, $_POST['name']);
        $code = mysqli_real_escape_string($connection, $_POST['code']);
        $symbol = mysqli_real_escape_string($connection, $_POST['symbol']);


        $sql = "INSERT INTO `$table` (`name`, `code`, `symbol`) VALUES('$name', '$code', '$symbol')";

        mysqli_query($connection, $sql) or die(mysqli_error($connection));
        $id = mysqli_insert_id($connection);




        $message = '<div class="alert alert-success" role="alert">' . $page . ' Added</div>';
        $id = mysqli_insert_id($connection);
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    } elseif ($_POST['action'] == "edit") {
        $name = mysqli_real_escape_string($connection, $_POST['name']);
        $code = mysqli_real_escape_string($connection, $_POST['code']);
        $symbol = mysqli_real_escape_string($connection, $_POST['symbol']);


        $id     = mysqli_real_escape_string($connection, $_POST['id']);


        $sql = "UPDATE `$table` SET 
`name` = '$name', 
`code` = '$code', 
`symbol` = '$symbol' $picture WHERE id='$id'";

        $result = mysqli_query($connection, $sql);

        $message = '<div class="alert alert-success" role="alert">' . $page . ' updated</div>';
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    } else {
        $message = '<div class="alert alert-danger" role="alert">Error occured please try again</div>';
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    }
}

?>
<style>
    .form-control {
        width: 100%;
    }

    .input-group-text {
        padding: 4px 14px 4px 0px;
        width: 50%;
        height: 34px;
    }

    .input-group-prepend {
        margin-right: -10px;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }

    .bootstrap-select.form-control-lg .dropdown-toggle {
        padding: 0.375rem 1rem;
    }
</style>
<!-- page content -->
<div class="content-wrapper">
    <section class="content-header">
        <div class="header-icon">
            <i class="fa fa-edit"></i>
        </div>
        <div class="header-title">
            <h1><?php echo $page ?> Management</h1>
            <small>Add, Update or Delete <?php echo $page ?></small>
        </div>
    </section>
    <section class="content">




        <div class="row">
            <div class="col-md-12 grid-margin">
                <div class="d-flex justify-content-between flex-wrap">
                    <div class="d-flex justify-content-between align-items-end flex-wrap">
                        <a href="?action=add" class="btn bb-bg btn-primary bb-bg mt-2 mt-xl-0"><i class="fa fa-plus"></i> Add <?php echo $page ?></a>
                    </div>
                </div>
            </div>
        </div>
        <br>
        <?php echo isset($message) ? $message : ''; ?> <div class="row">
            <div class="col-lg-12 grid-margin stretch-card">
                <div class="card card2">
                    <div class="card-body">
                        <?php if (isset($form)) { ?> <div class="cont1ainer">
                                <form id="dataForm" action="<?php echo $link; ?>" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                                    <?php if ($form == "edit") { ?>
                                        <input type="hidden" name="id" value="<?php echo $id; ?>">
                                    <?php } ?>
                                    <div class="row">
                                        <div class="form-group col-md-12">
                                            <label for="name">Name:</label>
                                            <input type="text" class="form-control" id="name" name="name" value="<?php echo isset($name) ? $name : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="code">Code:</label>
                                            <input type="text" class="form-control" id="code" name="code" value="<?php echo isset($code) ? $code : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="symbol">Symbol:</label>
                                            <input type="text" class="form-control" id="symbol" name="symbol" value="<?php echo isset($symbol) ? $symbol : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="mt-3 col-lg-12">
                                            <input type="hidden" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">
                                            <button type="submit" id="btnSubmit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        <?php } else { ?> <?php $query = "SELECT * FROM `$table`"; ?> <div class="table-responsive">
                                <table class="table table2" id="myTable">
                                    <thead>
                                        <tr>
                                            <th>Name</th>
                                            <th>Code</th>
                                            <th>Symbol</th>

                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $result = mysqli_query($connection, $query);
                                        while ($rows = mysqli_fetch_array($result)) {

                                            $action = " <a href='?action=edit&id=" . $rows['id'] . "' class='btn-info btn btn-sm'><i class='fa fa-pencil'></i></a> 
                <a href='?action=delete&id=" . $rows['id'] . "' onclick=\"return confirm('Are you sure you want to DELETE this Record?');\" class='btn-danger btn btn-sm'><i class='fa fa-trash-o'></i></a>";

                                            echo "
                                        <tr><td>" . $rows['name'] . "</td>
<td>" . $rows['code'] . "</td>
<td>" . $rows['symbol'] . "</td>

                    <td>$action</td>
                    </tr>";
                                        }
                                        ?></tbody>
                                </table>
                            </div> <?php } ?> </div>
                </div>
            </div>
        </div>

    </section>
</div>
<!-- /page content -->
<?php include("includes/footer.php"); ?><script>
    $(function() {
        $("#btnSubmit").on("click", function(e) {
            var form = $("#dataForm")[0];
            var isValid = form.checkValidity();

            if (!isValid) {
                e.preventDefault();
                e.stopPropagation();
            } else {
                form.submit();
            }
            form.classList.add('was-validated');
            return false; // For testing only to stay on this page
        });
    });
</script>