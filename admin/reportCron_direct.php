<?php
include_once('../includes/config.php');
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Shared\Date;


                $vendor_id = 4;
                $sql = "SELECT * FROM `vendors` WHERE `id` = '$vendor_id'";
                $result = mysqli_query($connection, $sql);
                $rows = mysqli_fetch_assoc($result);
                $vendor_id = $rows['id'];
                

                $inputFileName = "../[20240822140215133][CHN_CMI-MYS_Paragon][PL][USD][Premium][1]FinalPL.xls";
                $objPHPExcel = IOFactory::load($inputFileName);
                $sheet = $objPHPExcel->getSheet(0);
                $highestRow = $sheet->getHighestRow();
                $highestColumn = $sheet->getHighestColumn();
                
                // dest, code, price and date
                $rowStart = $rows['rowStart'];
                $destCell = $rows['dest'];
                $codeCell = $rows['code'];
                $priceCell = $rows['price'];
                $dateCell = $rows['date'];

                $lastDest = "";
                $lastPrice = "";
                
                for ($row = 0; $row <= $highestRow; ++$row) {
                    // get data of A6 cell only
                    $dest = $sheet->getCell($destCell.$rowStart)->getValue();
                    $code = $sheet->getCell($codeCell.$rowStart)->getValue();
                    $price = $sheet->getCell($priceCell.$rowStart)->getValue();
                    if($lastDest == $dest && $lastPrice == $price)
                    {
                        $rowStart++;
                        continue;
                    }
                    $lastDest = $dest;
                    $lastPrice = $price;
                    // $date contain A5-ALL tag then remove -ALL and get date from A5
                    
                    if (strpos($dateCell, '-All') !== false) {
                        $dateCellOne = str_replace("-All", "", $dateCell);
                        
                        $date = $sheet->getCell($dateCellOne)->getValue();
                        $date = strTODateRegx($date);
                    }
                    else
                    {
                        
                        $date = $sheet->getCell($dateCell.$rowStart)->getValue();
                        if (Date::isDateTime($sheet->getCell($dateCell.$rowStart))) {
                            $timestamp = Date::excelToTimestamp($date);
                            $date = date('Y-m-d', $timestamp); // Format it to Y-m-d
                        }
                        else if($date == "Immediate")
                        {
                            $date = date('Y-m-d', time());
                        }
                        else
                        {
                            $date = date("Y-m-d", strtotime($date));
                        }
                    }
                    
                    if($dest == "" || $code == "" || $price == "" || $date == "")
                    {
                        $rowStart++;
                        continue;
                    }
                    
                    
                    
                    if (strpos($dest, 'U.K.') !== false) {
                        $dest = str_replace("U.K.", "UNITED KINGDOM ", $dest);
                    }
                    if (preg_match('/^UK/', $dest)) {
                        $dest = preg_replace('/^UK/', 'UNITED KINGDOM ', $dest);
                    }
                    if (preg_match('/^USA/', $dest)) {
                        $dest = preg_replace('/^USA/', 'UNITED STATES ', $dest);
                    }
                    $dest = mysqli_real_escape_string($connection,$dest);
                    


                    // select destination if exist update rate and date
                    $sql = "SELECT * FROM `lcr` WHERE `vendor` = '$vendor_id' AND `destination` = '$dest' and `code` = '$code'";
                    echo $sql."<br>";
                    $result1 = mysqli_query($connection, $sql);
                    if (mysqli_num_rows($result1) > 0) {
                        $sql = "UPDATE `lcr` SET `price` = '$price', `date` = '$date' WHERE `vendor` = '$vendor_id' AND `destination` = '$dest' and `code` = '$code'";
                        echo $sql."<br>";
                        mysqli_query($connection, $sql);
                    } else {
                        $sql = "INSERT INTO `lcr`(`vendor`, `destination`, `code`, `price`, `date`) VALUES ('$vendor_id', '$dest', '$code', '$price', '$date')";
                        echo $sql."<br>";
                        mysqli_query($connection, $sql);
                    }
                    $rowStart++;
                }








function alphaToNumber($alpha)
{
    // A=0, B=1, C=2, D=3, E=4, F=5, G=6, H=7, I=8, J=9, K=10, L=11
    $alpha = strtoupper($alpha);
    $alpha = str_split($alpha);
    $number = 0;
    $length = count($alpha);
    for ($i = 0; $i < $length; $i++) {
        $number += (ord($alpha[$i]) - 65) * pow(26, $length - $i - 1);
    }
    return $number;
}


function strTODateRegx($string)
{
    $regex = '/(\b\d{1,2}[\/\-\.\s](?:\d{1,2}[\/\-\.\s])?\d{2,4}\b)|(\b\w+\s+\d{1,2}(?:st|nd|rd|th)?,?\s+\d{4})/i';

    // Match all possible dates
    if (preg_match_all($regex, $string, $matches)) {
        // Loop through all matched date strings
        foreach ($matches[0] as $dateString) {
            // Try different date formats
            $formats = ['Y-m-d', 'm/d/Y', 'd/m/Y', 'F j, Y', 'F jS, Y'];

            $validDate = false;

            // Try parsing the matched date with each format
            foreach ($formats as $format) {
                $date = DateTime::createFromFormat($format, $dateString);
                if ($date && $date->format($format) === $dateString) {
                    return $date->format('Y-m-d');
                    $validDate = true;
                    break;
                }
            }

            // If the date was not parsed using the predefined formats, try a fallback
            if (!$validDate) {
                $date = date_create($dateString);
                if ($date) {
                    return $date->format('Y-m-d');
                }
            }
        }
    }
}
