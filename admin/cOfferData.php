<?php
$page = "Client Data";
$link = "cOfferData.php";
$table = "cOfferData";
$back = "dashboard.php";
if (isset($_GET["action"]))
    $back = "cOfferData.php";
include("includes/header.php");
include("../includes/generalFunctions.class.php");

$general = new generalFunctions($connection);
$destinations = "";
if (isset($_SESSION["msg"]) && $_SESSION["msg"] != "") {
    $message = $_SESSION["msg"];
    unset($_SESSION["msg"]);
}
if (isset($_GET["action"])) {
    if ($_GET["action"] == "add") {
        $form = "add";
    } elseif ($_GET["action"] == "edit") {
        $form = "edit";
        $id       = mysqli_real_escape_string($connection, $_GET["id"]);
        $strSQL = mysqli_query($connection, "select * from `$table` where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $results    = mysqli_fetch_array($strSQL);
            $name       = $results['name'];
            $destination = $results['destination'];
            $type       = $results['type'];
            $asr        = $results['asr'];
            $acd        = $results['acd'];
            $rate       = $results['rate'];
            $ports      = $results['ports'];
            $mRate      = $results['mRate'];
            $added      = $results['added'];
            $remarks    = $results['remarks'];
            $deleteDate = $results['deleteDate'];
            $uid        = $results['uid'];
            $areas = explode(",",$results['areas']);
        } else {
            $message = '<div class="alert alert-danger" role="alert">Invalid ' . $page . '</div>';
            $_SESSION["msg"] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        }
    } elseif ($_GET['action'] == "delete") {
        $id       = mysqli_real_escape_string($connection, $_GET['id']);
        $strSQL = mysqli_query($connection, "select id from `$table` where id='" . $id . "'");
        if (mysqli_num_rows($strSQL) > 0) {
            $strSQL = mysqli_query($connection, "delete from `$table` where id='" . $id . "'");

            $message = '<div class="alert alert-danger" role="alert">' . $page . ' deleted</div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        } else {
            $message = '<div class="alert alert-danger" role="alert">Invalid ' . $page . '</div>';
            $_SESSION['msg'] = $message;
            echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
            exit;
        }
    }
}
if (isset($_POST['action'])) {
    if ($_POST['action'] == "add") {
        $name = mysqli_real_escape_string($connection, $_POST['name']);
        $destination = mysqli_real_escape_string($connection, $_POST['destination']);
        $type = mysqli_real_escape_string($connection, $_POST['type']);
        $asr = mysqli_real_escape_string($connection, $_POST['asr']);
        $acd = mysqli_real_escape_string($connection, $_POST['acd']);
        $rate = mysqli_real_escape_string($connection, $_POST['rate']);
        $ports = mysqli_real_escape_string($connection, $_POST['ports']);
        $mRate = mysqli_real_escape_string($connection, $_POST['mRate']);
        $added = mysqli_real_escape_string($connection, $_POST['added']);
        $remarks = mysqli_real_escape_string($connection, $_POST['remarks']);
        $deleteDate = mysqli_real_escape_string($connection, $_POST['deleteDate']);
        $uid = userid;
        $picture = "";
        $areas = $_POST['areas'];
        if(is_array($areas))
        {
            $areas = implode(",", $areas);
        }


        $sql = "INSERT INTO `$table` (`name`, `destination`, `type`, `asr`, `acd`, `rate`, `ports`, `mRate`, `added`, `remarks`, `deleteDate`,`uid`,`areas`) VALUES('$name', '$destination', '$type', '$asr', '$acd', '$rate', '$ports', '$mRate', '$added', '$remarks', '$deleteDate', '$uid', '$areas')";
        // echo $sql;

        mysqli_query($connection, $sql) or die(mysqli_error($connection));
        $id = mysqli_insert_id($connection);




        $message = '<div class="alert alert-success" role="alert">' . $page . ' Added</div>';
        $id = mysqli_insert_id($connection);
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    } elseif ($_POST['action'] == "edit") {
        $name = mysqli_real_escape_string($connection, $_POST['name']);
        $destination = mysqli_real_escape_string($connection, $_POST['destination']);
        $type = mysqli_real_escape_string($connection, $_POST['type']);
        $asr = mysqli_real_escape_string($connection, $_POST['asr']);
        $acd = mysqli_real_escape_string($connection, $_POST['acd']);
        $rate = mysqli_real_escape_string($connection, $_POST['rate']);
        $ports = mysqli_real_escape_string($connection, $_POST['ports']);
        $mRate = mysqli_real_escape_string($connection, $_POST['mRate']);
        $added = mysqli_real_escape_string($connection, $_POST['added']);
        $remarks = mysqli_real_escape_string($connection, $_POST['remarks']);
        $deleteDate = mysqli_real_escape_string($connection, $_POST['deleteDate']);
        $uid = userid;
        $picture = "";
        $areas = $_POST['areas'];
        if(is_array($areas))
        {
            $areas = implode(",", $areas);
        }

        $id     = mysqli_real_escape_string($connection, $_POST['id']);


        $sql = "UPDATE `$table` SET 
                    `name` = '$name', 
                    `destination` = '$destination', 
                    `type` = '$type', 
                    `asr` = '$asr', 
                    `acd` = '$acd', 
                    `rate` = '$rate', 
                    `ports` = '$ports', 
                    `mRate` = '$mRate', 
                    `added` = '$added', 
                    `remarks` = '$remarks', 
                    `uid` = '$uid', 
                    `areas` = '$areas', 
                    `deleteDate` = '$deleteDate' $picture WHERE id='$id'";

        $result = mysqli_query($connection, $sql);

        $message = '<div class="alert alert-success" role="alert">' . $page . ' updated</div>';
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    } else {
        $message = '<div class="alert alert-danger" role="alert">Error occured please try again</div>';
        $_SESSION['msg'] = $message;
        echo '<meta http-equiv="refresh" content="0;url=' . $link . '" />';
        exit;
    }
}

?>
<style>
    .form-control {
        width: 100%;
    }

    .input-group-text {
        padding: 4px 14px 4px 0px;
        width: 50%;
        height: 34px;
    }

    .input-group-prepend {
        margin-right: -10px;
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    input[type=number] {
        -moz-appearance: textfield;
    }

    .bootstrap-select.form-control-lg .dropdown-toggle {
        padding: 0.375rem 1rem;
    }
    .bootstrap-select.form-control-lg .dropdown-toggle {
        padding: 0.375rem 1rem;
    }
         .bootstrap-select.form-control-lg .dropdown-toggle {
            padding: 0.5rem 1rem;
            border: 1px solid #cccccc;
            background-color: white;
         }
      
</style>
<!-- page content -->
<div class="content-wrapper">
    <section class="content-header">
        <div class="header-icon">
            <i class="fa fa-edit"></i>
        </div>
        <div class="header-title">
            <h1><?php echo $page ?> Management</h1>
            <small>Add, Update or Delete <?php echo $page ?></small>
        </div>
    </section>
    <section class="content">




        <div class="row">
            <div class="col-md-12 grid-margin">
                <div class="d-flex justify-content-between flex-wrap">
                    <div class="d-flex justify-content-between align-items-end flex-wrap">
                        <a href="?action=add" class="btn bb-bg btn-primary bb-bg mt-2 mt-xl-0"><i class="fa fa-plus"></i> Add <?php echo $page ?></a>
                    </div>
                </div>
            </div>
        </div>
        <br>
        <?php echo isset($message) ? $message : ''; ?> <div class="row">
            <div class="col-lg-12 grid-margin stretch-card">
                <div class="card card2">
                    <div class="card-body">
                        <?php if (isset($form)) { ?>
                            <div class="cont1ainer">
                                <form id="dataForm" action="<?php echo $link; ?>" method="post" enctype="multipart/form-data" data-parsley-validate class="form-horizontal form-label-left">
                                    <?php if ($form == "edit") { ?>
                                        <input type="hidden" name="id" value="<?php echo $id; ?>">
                                    <?php } ?>
                                    <div class="row">
                                        <div class="form-group col-md-12">
                                            <label for="name">Client Name:</label>
                                            <input type="text" class="form-control" id="name" name="name" value="<?php echo isset($name) ? $name : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="destination">Destination:</label>
                                            <select name="destination" id="destination" class="form-control form-control-lg" required>
                                                <option value="">Select  *</option>
                                                <?php
                                                $query = "SELECT id, `name` FROM `destinations` where parent=0 order by `name`";
                                                $result = mysqli_query($connection, $query);

                                                while ($rows = mysqli_fetch_array($result)) {
                                                    if (isset($form) && ($form == "edit" || $form == "add") && $destination == $rows['id']) {
                                                ?>
                                                        <option selected="selected" value="<?php echo $rows['id']; ?>"><?php echo $rows['name']; ?></option>
                                                    <?php
                                                    } else {
                                                    ?>
                                                        <option value="<?php echo $rows['id']; ?>"><?php echo $rows['name']; ?></option>
                                                <?php
                                                    }
                                                }
                                                ?>
                                            </select>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-lg-6">
                                            <label for="areas">Areas:</label>
                                            <select name="areas[]" id="areas" multiple class="form-control form-control-lg selectpicker22" required>
                                                <option value="">Select  *</option>
                                                
                                            </select>
                                            <div class="invalid-feedback">Please select a customer</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="type">Type:</label>
                                            <!-- Cli / Ncli / CC  dropdown -->
                                            <select class="form-control" id="type" name="type" required>
                                                <option value="">Select Type</option>
                                                <option value="Cli" <?php echo (isset($type) && $type == 'Cli') ? 'selected' : ''; ?>>Cli</option>
                                                <option value="Ncli" <?php echo (isset($type) && $type == 'Ncli') ? 'selected' : ''; ?>>Ncli</option>
                                                <option value="CC" <?php echo (isset($type) && $type == 'CC') ? 'selected' : ''; ?>>CC</option>
                                            </select>
                                            <div class="invalid-feedback">Please Select a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="asr">ASR:</label>
                                            <input type="text" class="form-control" id="asr" name="asr" value="<?php echo isset($asr) ? $asr : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="acd">ACD:</label>
                                            <input type="text" class="form-control" id="acd" name="acd" value="<?php echo isset($acd) ? $acd : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="rate">Promo Rate:</label>
                                            <input type="number" step="0.00001" class="form-control" id="rate" name="rate" value="<?php echo isset($rate) ? $rate : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="ports">Ports Available:</label>
                                            <input type="text" class="form-control" id="ports" name="ports" value="<?php echo isset($ports) ? $ports : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="mRate">Market Rate:</label>
                                            <input type="number"  step="0.00001" class="form-control" id="mRate" name="mRate" value="<?php echo isset($mRate) ? $mRate : ''; ?>" required>
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="added">Date:</label>
                                            <input type="date" class="form-control" id="added" name="added" value="<?php echo isset($added) ? $added : ''; ?>">
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="deleteDate">Delete Date:</label>
                                            <input type="date" class="form-control" id="deleteDate" name="deleteDate" value="<?php echo isset($deleteDate) ? $deleteDate : ''; ?>">
                                            <div class="invalid-feedback">Please Enter a Value</div>
                                        </div>

                                        <div class="form-group col-md-12">
                                            <label for="remarks">Remarks:</label>
                                            <textarea name="remarks" class="form-control" id="remarks"><?php echo isset($remarks) ? $remarks : ''; ?></textarea>
                                        </div>
                                        
                                        <div class="mt-3 col-lg-12">
                                            <input type="hidden" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">
                                            <button type="submit" id="btnSubmit" class="btn btn-block btn-primary btn-lg font-weight-medium auth-form-btn" name="action" value="<?php echo ($form == "edit") ? 'edit' : 'add'; ?>">Save</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        <?php } else { ?>
                            <?php $query = "SELECT * FROM `$table`"; ?>
                            <div class="table-responsive">
                                <table class="table table2" id="myTable">
                                    <thead>
                                        <tr>
                                            <th>Client Name</th>
                                            <th>Destination</th>
                                            <th>Area</th>
                                            <th>Type</th>
                                            <th>ASR</th>
                                            <th>ACD</th>
                                            <th>Promo Rate</th>
                                            <th>Ports Available</th>
                                            <th>Market Rate</th>
                                            <th>Date</th>
                                            <th>Remarks</th>
                                            <th>Delete Date</th>
                                            <th>Added By</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $result = mysqli_query($connection, $query);
                                        while ($rows = mysqli_fetch_array($result)) {

                                            $action = " <a href='?action=edit&id=" . $rows['id'] . "' class='btn-info btn btn-sm'><i class='fa fa-pencil'></i></a> 
                                                        <a href='?action=delete&id=" . $rows['id'] . "' onclick=\"return confirm('Are you sure you want to DELETE this Record?');\" class='btn-danger btn btn-sm'><i class='fa fa-trash-o'></i></a>";
                                                        $areas = explode(",", $rows['areas']);
                                                        foreach($areas as $area)
                                                        {
                                                        
                                                        
                                                        if($area == -1)
                                                        {
                                                            $destinations = "All ".$general->getDestinationNameByID($rows['destination']);
                                                            $codes = $general->getDestinationCodes($rows['destination']);
                                                        }
                                                        else
                                                        {
                                                            $destinations = $general->getDestinationNameByID($area);
                                                            $codes = $general->getDestinationCodes($area);
                                                        }
                                            echo "<tr>
                                                    <td>" . $rows['name'] . "</td>
                                                    <td>" . $destinations . "</td>
                                                    <td>" . $codes . "</td>
                                                    <td>" . $rows['type'] . "</td>
                                                    <td>" . $rows['asr'] . "</td>
                                                    <td>" . $rows['acd'] . "</td>
                                                    <td>" . $rows['rate'] . "</td>
                                                    <td>" . $rows['ports'] . "</td>
                                                    <td>" . $rows['mRate'] . "</td>
                                                    <td>" . $rows['added'] . "</td>
                                                    <td>" . $rows['remarks'] . "</td>
                                                    <td>" . $rows['deleteDate'] . "</td>
                                                    <td>" . $general->getUserNameByID($rows['uid']) . "</td>
                                                    <td>$action</td>
                                                </tr>";
                                        }
                                    }
                                        ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<!-- /page content -->
<?php include("includes/footer.php"); ?>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/css/bootstrap-select.min.css">
<script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/js/bootstrap-select.min.js"></script>
<script>
    $(function() {
        $("#btnSubmit").on("click", function(e) {
            var form = $("#dataForm")[0];
            var isValid = form.checkValidity();

            if (!isValid) {
                e.preventDefault();
                e.stopPropagation();
            } else {
                form.submit();
            }
            form.classList.add('was-validated');
            return false; // For testing only to stay on this page
        });
    });
</script>
<script>
    $(function() {
        $('.selectpicker22').selectpicker({liveSearch:true});
        $('[data-toggle="tooltip"]').tooltip()
        
        $("#btnSubmit").on("click", function(e) {
            var form = $("#dataForm")[0];
            var isValid = form.checkValidity();

            if (!isValid) {
                e.preventDefault();
                e.stopPropagation();
            } else {
                form.submit();
            }
            form.classList.add('was-validated');
            return false; // For testing only to stay on this page
        });
        // on account dropdown change get prefix list
        $('#remarks').on('change', function() {
            $('.effectiveDiv').show();
            $('#effective').attr('disabled', false);
            // if remarks new update effective date to current date
            if ($(this).val() == 'New') {
                $('#effective').val(new Date().toISOString().slice(0, 10));
            }
            // if remarks increase update effective date to 7 days from current date
            if ($(this).val() == 'Increase') {
                var date = new Date();
                date.setDate(date.getDate() + 7);
                $('#effective').val(date.toISOString().slice(0, 10));
            }
            // if remarks decrease update effective date to current date
            if ($(this).val() == 'Decrease') {
                $('#effective').val(new Date().toISOString().slice(0, 10));
            }
            // if remarks no change update effective date hide .effectiveDiv
            if ($(this).val() == 'No Change') {
                // disable effective date
                $('#effective').attr('disabled', true);
                // hide effective date
                $('.effectiveDiv').hide();
            }
        });
        $('#destination').on('change', function() {
            var id = $(this).val();
            $.ajax({
                url: 'ajaxLoad.php',
                type: 'GET',
                data: {
                    action: 'getchildDestinations',
                    id: id
                },
                success: function(data) {
                    var areas = $('#areas');
                    areas.empty();
                    areas.append('<option value="-1">All Areas</option>');
                    // console.log(data);
                    $.each(data.areas, function(index, value) {
                        // console.log(index);
                        // console.log(value);
                        areas.append('<option value="' + index + '">' + value + '</option>');
                    });
                    $('.selectpicker22').selectpicker('destroy');
                    $('.selectpicker22').selectpicker();
                }
            });
        });
        <?php if (isset($form) && $form == "edit") { ?>
            
        var id = <?php echo $destination; ?>;
            $.ajax({
                url: 'ajaxLoad.php',
                type: 'GET',
                data: {
                    action: 'getchildDestinations',
                    id: id
                },
                success: function(data) {
                    var areas = $('#areas');
                    areas.empty();
                    areas.append('<option value="-1">All Areas</option>');
                    // console.log(data);
                    $.each(data.areas, function(index, value) {
                        // console.log(index);
                        // console.log(value);
                        areas.append('<option value="' + index + '">' + value + '</option>');
                    });
                    $('.selectpicker22').selectpicker('refresh');
                    // $('.selectpicker22').selectpicker();
                    // select picker multiple select coma seperated
                    $('.selectpicker22').selectpicker('val', <?php echo json_encode($areas); ?>);



                    

                    
                    // $('.selectpicker22').selectpicker({liveSearch:true});
                }
            });
        <?php } ?>
    });
</script>